﻿<?xml version="1.0" encoding="utf-8"?>
<TestRun id="3e5ec4b9-9865-4cbd-968e-e1308abd3b27" name="@ThinkPad-T450 2025-07-22 20:30:15" xmlns="http://microsoft.com/schemas/VisualStudio/TeamTest/2010">
  <Times creation="2025-07-22T20:30:15.7465736+03:00" queuing="2025-07-22T20:30:15.7465737+03:00" start="2025-07-22T20:30:11.5007944+03:00" finish="2025-07-22T20:30:18.6531706+03:00" />
  <TestSettings name="default" id="693075e7-011b-497d-9328-ebfc6c58c616">
    <Deployment runDeploymentRoot="_ThinkPad-T450_2025-07-22_20_30_15" />
  </TestSettings>
  <Results>
    <UnitTestResult executionId="957ac510-6840-46b7-87ac-d41c1bb1f4d2" testId="dcc55313-1af8-6c05-b202-ca2542d5df15" testName="AuthService.Tests.Integration.AuthServiceIntegrationTests.GetProfile_WithValidAuthentication_ReturnsOk" computerName="ThinkPad-T450" duration="00:00:00.2162829" startTime="2025-07-22T20:30:17.7256497+03:00" endTime="2025-07-22T20:30:17.7256499+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="957ac510-6840-46b7-87ac-d41c1bb1f4d2" />
    <UnitTestResult executionId="8306be4d-9a0f-4727-84ec-e13ce6f623ba" testId="bdd8ecdd-47f6-c264-9b5c-9cbb31aca9c2" testName="AuthService.Tests.Services.SupabaseClientAuthTests.LoginAsync_WithValidRequest_ReturnsAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.0322116" startTime="2025-07-22T20:30:15.6668405+03:00" endTime="2025-07-22T20:30:15.6668406+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8306be4d-9a0f-4727-84ec-e13ce6f623ba" />
    <UnitTestResult executionId="7586bc63-b86a-4390-86b2-d0e9faa1b06a" testId="58146934-21e4-220f-6edf-c193e8b1c7b4" testName="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SignInWithPhone_WithValidCredentials_ReturnsOkWithAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.0938279" startTime="2025-07-22T20:30:15.4884529+03:00" endTime="2025-07-22T20:30:15.4884530+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7586bc63-b86a-4390-86b2-d0e9faa1b06a" />
    <UnitTestResult executionId="42b2ac33-ef80-40de-a361-e16b8fe50b0c" testId="6d0be7e1-5ea1-e731-c019-467debb0369e" testName="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GetGoogleSignInUrl_WithoutRedirectTo_ReturnsOkWithOAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.0188517" startTime="2025-07-22T20:30:15.8326799+03:00" endTime="2025-07-22T20:30:15.8326800+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="42b2ac33-ef80-40de-a361-e16b8fe50b0c" />
    <UnitTestResult executionId="121c76f5-e9ed-4cdf-b34d-892f1a2fc08e" testId="3fc93f08-9bb2-d6b8-9d65-f53882199454" testName="AuthService.Tests.Services.SupabaseClientPhoneSignInTests.SignInWithPhoneAsync_SendsCorrectVerifyPayload" computerName="ThinkPad-T450" duration="00:00:00.0853093" startTime="2025-07-22T20:30:15.6168025+03:00" endTime="2025-07-22T20:30:15.6168026+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="121c76f5-e9ed-4cdf-b34d-892f1a2fc08e" />
    <UnitTestResult executionId="a21b40df-15c8-4b08-a3dd-df1d2799b55f" testId="1dda2492-2a69-841c-2f56-c16a40a30927" testName="AuthService.Tests.Integration.AuthServiceIntegrationTests.GetProfile_WithoutAuthentication_ReturnsUnauthorized" computerName="ThinkPad-T450" duration="00:00:00.1486267" startTime="2025-07-22T20:30:17.8759336+03:00" endTime="2025-07-22T20:30:17.8759338+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a21b40df-15c8-4b08-a3dd-df1d2799b55f" />
    <UnitTestResult executionId="16268f09-0e10-484d-b7fc-3c40ee144b0e" testId="4da301d5-2470-9322-a4aa-afc2e0f53a7a" testName="AuthService.Tests.Integration.EndToEndAuthFlowTests.CompleteAuthFlow_LoginRefreshProfile_ShouldWorkEndToEnd" computerName="ThinkPad-T450" duration="00:00:01.6539394" startTime="2025-07-22T20:30:17.4146117+03:00" endTime="2025-07-22T20:30:17.4146118+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="16268f09-0e10-484d-b7fc-3c40ee144b0e" />
    <UnitTestResult executionId="22b61b94-b643-41cc-9fe4-e8fb38a8feea" testId="15f461b5-3c95-d3fe-430d-8c64a68e5627" testName="AuthService.Tests.Services.SupabaseClientAuthTests.GetOAuthUrlAsync_WithValidRequest_ReturnsOAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.0019339" startTime="2025-07-22T20:30:15.6728460+03:00" endTime="2025-07-22T20:30:15.6728461+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="22b61b94-b643-41cc-9fe4-e8fb38a8feea" />
    <UnitTestResult executionId="ce806592-6f5e-4398-944f-dc922e3c917b" testId="eb519c66-e663-263b-a107-dffa550d05e6" testName="AuthService.Tests.Services.SupabaseClientGoogleSignInTests.SignInWithGoogleAsync_SendsCorrectRequestPayload" computerName="ThinkPad-T450" duration="00:00:00.0353571" startTime="2025-07-22T20:30:15.6333507+03:00" endTime="2025-07-22T20:30:15.6333509+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ce806592-6f5e-4398-944f-dc922e3c917b" />
    <UnitTestResult executionId="c4049ecd-e887-4a48-86fc-e13ed82a387d" testId="caa420c2-74fb-4520-17c9-3f0d88baa729" testName="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SignInWithPhone_WhenSupabaseClientThrowsGenericException_ReturnsInternalServerError" computerName="ThinkPad-T450" duration="00:00:00.0100917" startTime="2025-07-22T20:30:15.4955746+03:00" endTime="2025-07-22T20:30:15.4955747+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c4049ecd-e887-4a48-86fc-e13ed82a387d" />
    <UnitTestResult executionId="2140065a-4e4b-4115-83fc-3fc3bb5d2e9c" testId="9e5d1652-3ab0-6198-6809-7f6cb0f6e4ff" testName="AuthService.Tests.Services.SupabaseClientPhoneSignInTests.SendPhoneOtpAsync_WithValidPhoneNumber_ReturnsSuccessResponse" computerName="ThinkPad-T450" duration="00:00:00.0418290" startTime="2025-07-22T20:30:15.5381055+03:00" endTime="2025-07-22T20:30:15.5381057+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2140065a-4e4b-4115-83fc-3fc3bb5d2e9c" />
    <UnitTestResult executionId="aae14784-0adf-4053-a579-dd0c256cab57" testId="b6b8cab8-7d13-221a-0f9d-88f181ded8fb" testName="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GoogleCallback_WhenSupabaseClientThrowsGenericException_ReturnsInternalServerError" computerName="ThinkPad-T450" duration="00:00:00.0130046" startTime="2025-07-22T20:30:15.7895662+03:00" endTime="2025-07-22T20:30:15.7895663+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="aae14784-0adf-4053-a579-dd0c256cab57" />
    <UnitTestResult executionId="d51b78fa-99d7-49e1-8088-5f4bd929baaa" testId="f8bb0f29-2c5b-9521-2650-3f13c4c7a2af" testName="AuthService.Tests.Services.SupabaseClientAuthTests.RecoverPasswordAsync_WithValidRequest_ReturnsSuccessResponse" computerName="ThinkPad-T450" duration="00:00:00.0385989" startTime="2025-07-22T20:30:15.6331848+03:00" endTime="2025-07-22T20:30:15.6331849+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d51b78fa-99d7-49e1-8088-5f4bd929baaa" />
    <UnitTestResult executionId="5e07640d-7930-42be-97f2-bba54787a44c" testId="10006d2d-43da-e919-7d8d-3618eeec3942" testName="AuthService.Tests.Integration.TokenRefreshIntegrationTests.GeneratedToken_ShouldBeValidForAuthentication" computerName="ThinkPad-T450" duration="00:00:00.0065737" startTime="2025-07-22T20:30:17.1951611+03:00" endTime="2025-07-22T20:30:17.1951612+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5e07640d-7930-42be-97f2-bba54787a44c" />
    <UnitTestResult executionId="bb821071-2949-4644-a519-a068a473e03a" testId="cc42a13d-6607-f477-4b2e-c6f3ee233e53" testName="AuthService.Tests.Services.SupabaseClientGoogleSignInTests.SignInWithGoogleAsync_WithInvalidCode_ThrowsHttpRequestException" computerName="ThinkPad-T450" duration="00:00:00.1532462" startTime="2025-07-22T20:30:15.5594646+03:00" endTime="2025-07-22T20:30:15.5594648+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="bb821071-2949-4644-a519-a068a473e03a" />
    <UnitTestResult executionId="2217d857-5405-4a55-8b09-bbe3278b8118" testId="c49fa5e8-ed98-8e4f-3637-9fae643c31fd" testName="AuthService.Tests.Integration.AuthServiceIntegrationTests.GetProfile_WhenProfileNotFound_ReturnsNotFound" computerName="ThinkPad-T450" duration="00:00:00.1873488" startTime="2025-07-22T20:30:18.3222599+03:00" endTime="2025-07-22T20:30:18.3222601+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2217d857-5405-4a55-8b09-bbe3278b8118" />
    <UnitTestResult executionId="7a30c7fb-cd5e-4fca-9509-36ec2f39873b" testId="b6e911f7-39f5-0dca-5d60-d342617c3eac" testName="AuthService.Tests.Integration.TokenRefreshIntegrationTests.RefreshToken_WithInvalidRefreshToken_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:00.2601982" startTime="2025-07-22T20:30:17.4568017+03:00" endTime="2025-07-22T20:30:17.4568019+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7a30c7fb-cd5e-4fca-9509-36ec2f39873b" />
    <UnitTestResult executionId="cca32b01-d3f6-4693-b29d-74ab97d557ed" testId="be06c46a-fec5-0bf0-f9d5-071b76119fe9" testName="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SendPhoneOtp_WithEmptyPhoneNumber_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:00.0114883" startTime="2025-07-22T20:30:15.4954547+03:00" endTime="2025-07-22T20:30:15.4954548+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cca32b01-d3f6-4693-b29d-74ab97d557ed" />
    <UnitTestResult executionId="6efb7230-163d-4956-aebf-6e536450d947" testId="7fc034ae-e9d5-f61f-1692-aa25f95679a8" testName="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SendPhoneOtp_WithValidPhoneNumber_ReturnsOkWithSuccessResponse" computerName="ThinkPad-T450" duration="00:00:00.0378898" startTime="2025-07-22T20:30:15.4956944+03:00" endTime="2025-07-22T20:30:15.4956945+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6efb7230-163d-4956-aebf-6e536450d947" />
    <UnitTestResult executionId="0afd00fd-755b-4ed4-b11e-203319c1ba6e" testId="a7d182fe-b17a-cf44-4dba-fc0d1bbc44fd" testName="AuthService.Tests.Services.SupabaseClientAuthTests.SignupAsync_WithValidRequest_ReturnsAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.0225818" startTime="2025-07-22T20:30:15.7375574+03:00" endTime="2025-07-22T20:30:15.7375575+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0afd00fd-755b-4ed4-b11e-203319c1ba6e" />
    <UnitTestResult executionId="3cbe30f1-3c35-4aff-b308-346531b903e9" testId="c30ab486-4bb4-f5a4-8b91-a186825a2153" testName="AuthService.Tests.Controllers.AuthControllerTests.GetProfile_WithValidUserId_ReturnsOkWithProfile" computerName="ThinkPad-T450" duration="00:00:00.0090127" startTime="2025-07-22T20:30:16.0535905+03:00" endTime="2025-07-22T20:30:16.0535907+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3cbe30f1-3c35-4aff-b308-346531b903e9" />
    <UnitTestResult executionId="d1c83587-0a90-41b1-b369-1e6e23ca3ee9" testId="938e4b5d-a938-ac07-4668-282fb1dcb486" testName="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SignInWithPhone_WithEmptyPhoneNumber_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:00.0046342" startTime="2025-07-22T20:30:15.4767877+03:00" endTime="2025-07-22T20:30:15.4767878+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d1c83587-0a90-41b1-b369-1e6e23ca3ee9" />
    <UnitTestResult executionId="f9c36708-e500-44df-b7a0-dbba557e54bf" testId="bd6bca39-cf93-5a74-f19f-f5add9766961" testName="AuthService.Tests.Controllers.AuthControllerTests.Login_WithValidRequest_ReturnsOkWithAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.0078303" startTime="2025-07-22T20:30:16.0206149+03:00" endTime="2025-07-22T20:30:16.0206150+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f9c36708-e500-44df-b7a0-dbba557e54bf" />
    <UnitTestResult executionId="b4e18b3e-f572-4e38-9de4-fc193c170c47" testId="baee4509-f6ed-7368-825a-ebd80d90ef9e" testName="AuthService.Tests.Integration.TokenRefreshIntegrationTests.RefreshToken_WithValidRefreshToken_ReturnsNewTokens" computerName="ThinkPad-T450" duration="00:00:00.2839139" startTime="2025-07-22T20:30:17.7435884+03:00" endTime="2025-07-22T20:30:17.7435886+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b4e18b3e-f572-4e38-9de4-fc193c170c47" />
    <UnitTestResult executionId="26cc6063-032e-4bf4-a4a6-239901a39e8d" testId="dd6f1ef8-bcdf-0087-13cc-81d56c9785c7" testName="AuthService.Tests.Integration.AuthServiceIntegrationTests.GetUserProfile_WithoutAuthentication_ReturnsUnauthorized" computerName="ThinkPad-T450" duration="00:00:00.2463332" startTime="2025-07-22T20:30:18.1313484+03:00" endTime="2025-07-22T20:30:18.1313493+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="26cc6063-032e-4bf4-a4a6-239901a39e8d" />
    <UnitTestResult executionId="1645cdf2-e779-4331-89c7-f31581a06884" testId="24b67f52-899d-1fe0-df0c-60ba72860d1f" testName="AuthService.Tests.Services.SupabaseClientGoogleSignInTests.SignInWithGoogleAsync_WithValidCode_ReturnsAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.0967177" startTime="2025-07-22T20:30:15.7281513+03:00" endTime="2025-07-22T20:30:15.7281515+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1645cdf2-e779-4331-89c7-f31581a06884" />
    <UnitTestResult executionId="53e587ec-7d80-4b6c-b288-b5a5f8a3ea45" testId="231a7169-21b7-3451-bb18-bb1321339130" testName="AuthService.Tests.Integration.TokenValidationTests.GenerateTestToken_ShouldCreateValidJWT" computerName="ThinkPad-T450" duration="00:00:00.0042928" startTime="2025-07-22T20:30:16.4587638+03:00" endTime="2025-07-22T20:30:16.4587640+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="53e587ec-7d80-4b6c-b288-b5a5f8a3ea45" />
    <UnitTestResult executionId="ad397541-c980-49f4-9f6e-eaa5612ef2bc" testId="298789a5-58c0-19ca-cab2-72695144cbe0" testName="AuthService.Tests.Integration.TokenValidationTests.ValidateSupabaseTokenFormat_ShouldMatchExpectedStructure" computerName="ThinkPad-T450" duration="00:00:00.1225420" startTime="2025-07-22T20:30:16.4307451+03:00" endTime="2025-07-22T20:30:16.4307453+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ad397541-c980-49f4-9f6e-eaa5612ef2bc" />
    <UnitTestResult executionId="2b1958ce-1bef-445b-9d1c-4968c686675e" testId="142e657f-2304-4556-4a6b-0942562be76b" testName="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GoogleCallback_WithEmptyCode_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:00.0145511" startTime="2025-07-22T20:30:15.7504118+03:00" endTime="2025-07-22T20:30:15.7504120+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2b1958ce-1bef-445b-9d1c-4968c686675e" />
    <UnitTestResult executionId="9eea3810-4f9d-443d-a4fb-070794f555ce" testId="e6f4805a-99ff-78ac-bf63-1c6532349164" testName="AuthService.Tests.Services.SupabaseClientGoogleSignInTests.GetGoogleSignInUrlAsync_WithRedirectTo_ReturnsValidOAuthResponseWithRedirect" computerName="ThinkPad-T450" duration="00:00:00.0261563" startTime="2025-07-22T20:30:15.5887759+03:00" endTime="2025-07-22T20:30:15.5887760+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9eea3810-4f9d-443d-a4fb-070794f555ce" />
    <UnitTestResult executionId="9c5a869c-57d8-40a4-a9b7-34e3d73cf4d7" testId="75a9faa9-1914-1f35-d21e-8bcd8d9525a5" testName="AuthService.Tests.Controllers.AuthControllerTests.Signup_WithInvalidRequest_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:00.0087417" startTime="2025-07-22T20:30:16.0632027+03:00" endTime="2025-07-22T20:30:16.0632028+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9c5a869c-57d8-40a4-a9b7-34e3d73cf4d7" />
    <UnitTestResult executionId="29c6d617-e343-4882-9f87-f04b622366ee" testId="f3d85af9-364c-6692-a0ce-40ccd2ec6408" testName="AuthService.Tests.Controllers.AuthControllerTests.Signup_WithValidRequest_ReturnsOkWithAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.1755267" startTime="2025-07-22T20:30:15.9097967+03:00" endTime="2025-07-22T20:30:15.9097968+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="29c6d617-e343-4882-9f87-f04b622366ee" />
    <UnitTestResult executionId="001eec8f-719a-42ae-9234-476cc9efb17d" testId="6051ab2d-09ef-0f41-0094-e86ec50c0d5f" testName="AuthService.Tests.Controllers.AuthControllerTests.SendOtp_WithValidRequest_ReturnsOkWithSuccessResponse" computerName="ThinkPad-T450" duration="00:00:00.0145461" startTime="2025-07-22T20:30:16.0356576+03:00" endTime="2025-07-22T20:30:16.0356577+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="001eec8f-719a-42ae-9234-476cc9efb17d" />
    <UnitTestResult executionId="c04edee7-7d98-4bf0-8665-9d8754fe8b8e" testId="e305b7b5-2bd4-8b70-a096-1f3a8a1df786" testName="AuthService.Tests.Integration.AuthServiceIntegrationTests.HealthCheck_ReturnsOk" computerName="ThinkPad-T450" duration="00:00:00.1201407" startTime="2025-07-22T20:30:18.4453390+03:00" endTime="2025-07-22T20:30:18.4453393+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c04edee7-7d98-4bf0-8665-9d8754fe8b8e" />
    <UnitTestResult executionId="e203147c-65cd-44a6-ab82-2191bbaa668e" testId="d9b42c1a-d084-d300-09b0-6f113e3daa15" testName="AuthService.Tests.Controllers.AuthControllerTests.RecoverPassword_WithValidEmail_ReturnsOkWithSuccessResponse" computerName="ThinkPad-T450" duration="00:00:00.0199770" startTime="2025-07-22T20:30:16.0871591+03:00" endTime="2025-07-22T20:30:16.0871592+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e203147c-65cd-44a6-ab82-2191bbaa668e" />
    <UnitTestResult executionId="b8100ddc-3b7f-444d-82c5-ef51c3176261" testId="9794b204-e118-68a7-8104-948da1699e62" testName="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SendPhoneOtp_WithNullPhoneNumber_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:00.6179988" startTime="2025-07-22T20:30:15.1410632+03:00" endTime="2025-07-22T20:30:15.1411216+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b8100ddc-3b7f-444d-82c5-ef51c3176261" />
    <UnitTestResult executionId="9a981af0-d918-46d0-9530-9b0558e097b4" testId="181a6165-cade-de23-63de-7e7070e84fe0" testName="AuthService.Tests.Integration.TokenValidationTests.ValidateToken_WithWrongIssuer_ShouldFail" computerName="ThinkPad-T450" duration="00:00:00.0228179" startTime="2025-07-22T20:30:16.4553054+03:00" endTime="2025-07-22T20:30:16.4553055+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9a981af0-d918-46d0-9530-9b0558e097b4" />
    <UnitTestResult executionId="eae77470-52d3-49b6-a882-131611d15e1f" testId="91f56c33-9900-b7fd-cc2e-3c9e7051dbc1" testName="AuthService.Tests.Services.SupabaseClientTests.GetProfileAsync_WithEmptyUserId_ThrowsArgumentException" computerName="ThinkPad-T450" duration="00:00:00.0089627" startTime="2025-07-22T20:30:16.3274989+03:00" endTime="2025-07-22T20:30:16.3274991+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="eae77470-52d3-49b6-a882-131611d15e1f" />
    <UnitTestResult executionId="f1884c51-02a9-42c1-bf3f-9b7b9b0d8925" testId="655fb69b-86c8-f590-19e1-299d9cef0e00" testName="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GoogleCallback_WithValidCode_ReturnsOkWithAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.0173838" startTime="2025-07-22T20:30:15.8542250+03:00" endTime="2025-07-22T20:30:15.8542252+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f1884c51-02a9-42c1-bf3f-9b7b9b0d8925" />
    <UnitTestResult executionId="4043d7d3-c1a7-4a2c-87c0-635e144aeca1" testId="6d86cab8-79cd-6c2d-2860-dbdf7e56def4" testName="AuthService.Tests.Integration.TokenValidationTests.ValidateToken_WithWrongAudience_ShouldFail" computerName="ThinkPad-T450" duration="00:00:00.4388005" startTime="2025-07-22T20:30:16.3079246+03:00" endTime="2025-07-22T20:30:16.3079248+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4043d7d3-c1a7-4a2c-87c0-635e144aeca1" />
    <UnitTestResult executionId="2e2ff90b-e0a5-4e97-9aa2-3e0499969a48" testId="2bc7c15f-1a7b-cfaa-ca12-3b5228df51be" testName="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SignInWithPhone_WithEmptyOtpCode_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:00.0024389" startTime="2025-07-22T20:30:15.4889792+03:00" endTime="2025-07-22T20:30:15.4889793+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2e2ff90b-e0a5-4e97-9aa2-3e0499969a48" />
    <UnitTestResult executionId="7ed2b8be-8046-4087-9e6a-c4fce6140bcb" testId="3dd4fea6-b413-4334-2bea-1b8bc69be77b" testName="AuthService.Tests.Controllers.AuthControllerTests.GetProfile_WithEmptyUserId_ReturnsUnauthorized" computerName="ThinkPad-T450" duration="00:00:00.0091524" startTime="2025-07-22T20:30:16.0961019+03:00" endTime="2025-07-22T20:30:16.0961020+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7ed2b8be-8046-4087-9e6a-c4fce6140bcb" />
    <UnitTestResult executionId="96bfe423-c5e8-460c-8857-d5f03de0ae0f" testId="c33db39c-f8d8-e5de-3b32-71a91c0121ad" testName="AuthService.Tests.Controllers.AuthControllerTests.GetProfile_WithWhitespaceUserId_ReturnsUnauthorized" computerName="ThinkPad-T450" duration="00:00:00.0059100" startTime="2025-07-22T20:30:16.1053918+03:00" endTime="2025-07-22T20:30:16.1053920+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="96bfe423-c5e8-460c-8857-d5f03de0ae0f" />
    <UnitTestResult executionId="0fead543-8e79-45a0-8609-58532c3a81c5" testId="de3db4b9-15b4-c621-9c9e-87b03edad4a7" testName="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GetGoogleSignInUrl_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:00.0202678" startTime="2025-07-22T20:30:15.8688749+03:00" endTime="2025-07-22T20:30:15.8688751+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0fead543-8e79-45a0-8609-58532c3a81c5" />
    <UnitTestResult executionId="5ece9271-7589-472e-a287-03efc903f5ad" testId="08f9ef06-54fc-2a42-815a-d5c1222316e8" testName="AuthService.Tests.Integration.EndToEndAuthFlowTests.TokenValidation_GeneratedTokenStructure_ShouldBeValid" computerName="ThinkPad-T450" duration="00:00:00.0254011" startTime="2025-07-22T20:30:17.4641396+03:00" endTime="2025-07-22T20:30:17.4641398+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5ece9271-7589-472e-a287-03efc903f5ad" />
    <UnitTestResult executionId="4e78e249-0346-463d-9963-daa2b8496c77" testId="08c3aac2-17ea-b0dc-fbfd-b158f9f54ca4" testName="AuthService.Tests.Services.SupabaseClientPhoneSignInTests.SignInWithPhoneAsync_WithInvalidOtp_ThrowsHttpRequestException" computerName="ThinkPad-T450" duration="00:00:01.0008379" startTime="2025-07-22T20:30:15.5085386+03:00" endTime="2025-07-22T20:30:15.5085387+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4e78e249-0346-463d-9963-daa2b8496c77" />
    <UnitTestResult executionId="c2f38066-6dca-42ef-b5bc-b074949e9236" testId="8873292f-c258-7eef-1ddc-d40490b6a8f3" testName="AuthService.Tests.Services.SupabaseClientTests.GetProfileAsync_BuildsCorrectUrl" computerName="ThinkPad-T450" duration="00:00:00.0195003" startTime="2025-07-22T20:30:16.2790792+03:00" endTime="2025-07-22T20:30:16.2790794+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c2f38066-6dca-42ef-b5bc-b074949e9236" />
    <UnitTestResult executionId="7791edd2-9593-4a99-9973-515b6aa72e3d" testId="11a5e625-f15c-9949-6c39-5c7006b4a8fc" testName="AuthService.Tests.Services.SupabaseClientAuthTests.SendOtpAsync_WithValidRequest_ReturnsSuccessResponse" computerName="ThinkPad-T450" duration="00:00:00.9432508" startTime="2025-07-22T20:30:15.5078781+03:00" endTime="2025-07-22T20:30:15.5078782+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7791edd2-9593-4a99-9973-515b6aa72e3d" />
    <UnitTestResult executionId="29736f69-5e1b-4ebc-91b2-e74c7bb901fd" testId="03a04153-62c0-7445-6fc2-bb2aaf958a13" testName="AuthService.Tests.Services.SupabaseClientAuthTests.SignupAsync_WithHttpError_ThrowsHttpRequestException" computerName="ThinkPad-T450" duration="00:00:00.0294252" startTime="2025-07-22T20:30:15.7083459+03:00" endTime="2025-07-22T20:30:15.7083461+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="29736f69-5e1b-4ebc-91b2-e74c7bb901fd" />
    <UnitTestResult executionId="4fe6d366-1cb8-4ec1-9b60-60b006c5d6e8" testId="6870dd26-7828-dd97-05c2-7045718815eb" testName="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SignInWithPhone_WithNullValues_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:00.0072931" startTime="2025-07-22T20:30:15.5072387+03:00" endTime="2025-07-22T20:30:15.5072388+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4fe6d366-1cb8-4ec1-9b60-60b006c5d6e8" />
    <UnitTestResult executionId="6a1280e0-f625-42be-9d0c-105c14189947" testId="70115306-a79f-e16b-9299-d892c167492b" testName="AuthService.Tests.Controllers.AuthControllerTests.RefreshToken_WithValidRequest_ReturnsOkWithAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.0438713" startTime="2025-07-22T20:30:16.0086573+03:00" endTime="2025-07-22T20:30:16.0086574+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6a1280e0-f625-42be-9d0c-105c14189947" />
    <UnitTestResult executionId="e76e1fb4-3c9e-4d90-8730-1e1e506dd61c" testId="72201805-3427-4a50-77a4-4b3ca1683a1e" testName="AuthService.Tests.Controllers.AuthControllerTests.GetProfile_WithNoSubClaim_ReturnsUnauthorized" computerName="ThinkPad-T450" duration="00:00:00.0085058" startTime="2025-07-22T20:30:16.0450832+03:00" endTime="2025-07-22T20:30:16.0450833+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e76e1fb4-3c9e-4d90-8730-1e1e506dd61c" />
    <UnitTestResult executionId="cb4fb35d-cd2e-4d12-96e1-894bb67d61ab" testId="53d0614e-2517-0c33-b00b-924b152e610d" testName="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SendPhoneOtp_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:00.0243857" startTime="2025-07-22T20:30:15.4886786+03:00" endTime="2025-07-22T20:30:15.4886787+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cb4fb35d-cd2e-4d12-96e1-894bb67d61ab" />
    <UnitTestResult executionId="81468877-ce65-4337-b651-7a0f457b5186" testId="2c104947-53f9-cbb1-6748-7014c2fd731b" testName="AuthService.Tests.Services.SupabaseClientAuthTests.RefreshTokenAsync_WithValidRequest_ReturnsAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.1609435" startTime="2025-07-22T20:30:15.5866032+03:00" endTime="2025-07-22T20:30:15.5866034+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="81468877-ce65-4337-b651-7a0f457b5186" />
    <UnitTestResult executionId="c33453d5-5f2a-422a-b6ab-9ed42675f35d" testId="c2c30624-746b-5b1a-b818-5a035d4d4caa" testName="AuthService.Tests.Services.SupabaseClientTests.GetProfileAsync_WhenHttpRequestFails_ThrowsHttpRequestException" computerName="ThinkPad-T450" duration="00:00:00.0191810" startTime="2025-07-22T20:30:16.2247619+03:00" endTime="2025-07-22T20:30:16.2247621+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c33453d5-5f2a-422a-b6ab-9ed42675f35d" />
    <UnitTestResult executionId="e2432095-9633-4afa-a25c-db80fbb91670" testId="e4a28ef9-b765-5174-4de1-0ed0d8e7ab74" testName="AuthService.Tests.Services.SupabaseClientPhoneSignInTests.SendPhoneOtpAsync_WithCreateUserFalse_SendsCorrectPayload" computerName="ThinkPad-T450" duration="00:00:00.0169493" startTime="2025-07-22T20:30:15.6371353+03:00" endTime="2025-07-22T20:30:15.6371354+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e2432095-9633-4afa-a25c-db80fbb91670" />
    <UnitTestResult executionId="ecdf7b7e-d20a-4539-8e7e-bbac3ea516e5" testId="1e4cb761-0012-56d3-ef03-80b585a86a5a" testName="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GoogleCallback_WithNullCode_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:00.0013092" startTime="2025-07-22T20:30:15.8114819+03:00" endTime="2025-07-22T20:30:15.8114819+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ecdf7b7e-d20a-4539-8e7e-bbac3ea516e5" />
    <UnitTestResult executionId="d962df67-76d4-4ec2-8774-02bfd3383c47" testId="0be9325a-a529-bd7a-cf85-b0c9d7feaef7" testName="AuthService.Tests.Controllers.AuthControllerTests.GetProfile_WithNullProfile_ReturnsNotFound" computerName="ThinkPad-T450" duration="00:00:00.0570635" startTime="2025-07-22T20:30:15.9672079+03:00" endTime="2025-07-22T20:30:15.9672080+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d962df67-76d4-4ec2-8774-02bfd3383c47" />
    <UnitTestResult executionId="a83cfc1a-0aa0-4907-9e0b-7e4dbe84469a" testId="d007daa9-e0fd-8df4-063d-fca1ce656c34" testName="AuthService.Tests.Services.SupabaseClientTests.GetProfileAsync_WhenNoProfileFound_ReturnsNull" computerName="ThinkPad-T450" duration="00:00:00.0278379" startTime="2025-07-22T20:30:16.2589140+03:00" endTime="2025-07-22T20:30:16.2589141+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a83cfc1a-0aa0-4907-9e0b-7e4dbe84469a" />
    <UnitTestResult executionId="308b3d08-c303-44c6-833b-7b0c1b6f365c" testId="8e4017d4-ae1c-287a-6240-9e9b04e85e6a" testName="AuthService.Tests.Controllers.AuthControllerTests.GetProfile_WhenSupabaseThrowsException_ReturnsInternalServerError" computerName="ThinkPad-T450" duration="00:00:00.0198074" startTime="2025-07-22T20:30:16.1231938+03:00" endTime="2025-07-22T20:30:16.1231939+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="308b3d08-c303-44c6-833b-7b0c1b6f365c" />
    <UnitTestResult executionId="a33b6329-d46f-4eab-8900-2a0010bb613c" testId="20ad6935-f366-3400-b44a-34fa0ce5a57a" testName="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GetGoogleSignInUrl_WithRedirectTo_ReturnsOkWithOAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.0208447" startTime="2025-07-22T20:30:15.7731079+03:00" endTime="2025-07-22T20:30:15.7731080+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a33b6329-d46f-4eab-8900-2a0010bb613c" />
    <UnitTestResult executionId="87e528ea-2a6e-4772-b72e-f3160b9cb7af" testId="a32aabcf-93a8-a4b1-2c17-f956159715e0" testName="AuthService.Tests.Integration.AuthServiceIntegrationTests.GetUserMe_WithValidAuthentication_ReturnsOk" computerName="ThinkPad-T450" duration="00:00:00.8194913" startTime="2025-07-22T20:30:17.1813824+03:00" endTime="2025-07-22T20:30:17.1813826+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="87e528ea-2a6e-4772-b72e-f3160b9cb7af" />
    <UnitTestResult executionId="02f181f7-d634-4cbe-8e07-495305451eb6" testId="823b97b5-c1cc-6dbc-25cc-8ad914b06053" testName="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SendPhoneOtp_WhenSupabaseClientThrowsGenericException_ReturnsInternalServerError" computerName="ThinkPad-T450" duration="00:00:00.0118047" startTime="2025-07-22T20:30:15.4953073+03:00" endTime="2025-07-22T20:30:15.4953074+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="02f181f7-d634-4cbe-8e07-495305451eb6" />
    <UnitTestResult executionId="90415933-1853-450f-af00-24d41804a30f" testId="606912b3-3a47-4736-7d13-11fdfa443b80" testName="AuthService.Tests.Services.SupabaseClientPhoneSignInTests.SignInWithPhoneAsync_WithValidCredentials_ReturnsAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.0879675" startTime="2025-07-22T20:30:15.7241236+03:00" endTime="2025-07-22T20:30:15.7241237+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="90415933-1853-450f-af00-24d41804a30f" />
    <UnitTestResult executionId="294eb818-16e4-49d4-a180-1d49c01cb740" testId="aae8c18b-e2be-cdf7-7079-9c04515c93e2" testName="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SignInWithPhone_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:00.0096132" startTime="2025-07-22T20:30:15.4888534+03:00" endTime="2025-07-22T20:30:15.4888535+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="294eb818-16e4-49d4-a180-1d49c01cb740" />
    <UnitTestResult executionId="f238f2c0-2c65-47ea-bf21-d9b655c2cb11" testId="ec16a05a-31c1-ca52-0862-89763bdffbc8" testName="AuthService.Tests.Services.SupabaseClientGoogleSignInTests.GetGoogleSignInUrlAsync_WithoutRedirectTo_ReturnsValidOAuthResponse" computerName="ThinkPad-T450" duration="00:00:00.0056743" startTime="2025-07-22T20:30:15.7372331+03:00" endTime="2025-07-22T20:30:15.7372333+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f238f2c0-2c65-47ea-bf21-d9b655c2cb11" />
    <UnitTestResult executionId="7bd94441-01c6-4270-8024-75f4f27ff20c" testId="6899c30f-b2af-54c4-38c5-ed8ae4c7e1fd" testName="AuthService.Tests.Services.SupabaseClientTests.GetProfileAsync_WithValidUserId_ReturnsSupabaseUser" computerName="ThinkPad-T450" duration="00:00:00.0374447" startTime="2025-07-22T20:30:16.3166882+03:00" endTime="2025-07-22T20:30:16.3166884+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7bd94441-01c6-4270-8024-75f4f27ff20c" />
    <UnitTestResult executionId="61b9a566-9751-48e4-8b8d-80e9560d812c" testId="80a3f711-771a-7680-feaf-0ce341ed3c25" testName="AuthService.Tests.Integration.TokenRefreshIntegrationTests.RefreshToken_WithEmptyRefreshToken_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:02.6678280" startTime="2025-07-22T20:30:17.1770241+03:00" endTime="2025-07-22T20:30:17.1770242+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="61b9a566-9751-48e4-8b8d-80e9560d812c" />
    <UnitTestResult executionId="a76d5ffb-bd19-4c66-9ef7-d8d5c20b7d8d" testId="0f6ed8c4-f25e-11a7-b9aa-6b2127216740" testName="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GoogleCallback_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest" computerName="ThinkPad-T450" duration="00:00:00.0223175" startTime="2025-07-22T20:30:15.8113188+03:00" endTime="2025-07-22T20:30:15.8113190+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a76d5ffb-bd19-4c66-9ef7-d8d5c20b7d8d" />
    <UnitTestResult executionId="2b3a8667-277d-433a-92c0-13ff296c4410" testId="7163ecf3-3bef-526f-20a6-3b1a06a49e89" testName="AuthService.Tests.Services.SupabaseClientTests.GetProfileAsync_SetsCorrectHeaders" computerName="ThinkPad-T450" duration="00:00:00.0744274" startTime="2025-07-22T20:30:16.2078310+03:00" endTime="2025-07-22T20:30:16.2078312+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2b3a8667-277d-433a-92c0-13ff296c4410" />
    <UnitTestResult executionId="8794dc18-1c16-44f2-9d83-952bd8bef5cf" testId="6da05667-3bd9-a52d-e7d5-5b25bbd25a58" testName="AuthService.Tests.Services.SupabaseClientPhoneSignInTests.SendPhoneOtpAsync_WithInvalidPhoneNumber_ThrowsHttpRequestException" computerName="ThinkPad-T450" duration="00:00:00.0036680" startTime="2025-07-22T20:30:15.7304051+03:00" endTime="2025-07-22T20:30:15.7304053+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8794dc18-1c16-44f2-9d83-952bd8bef5cf" />
    <UnitTestResult executionId="1e277329-a3ff-434e-9343-883ad6ff0dc9" testId="c4a799ef-1926-aa9f-239e-d8bb2edfb77d" testName="AuthService.Tests.Integration.AuthServiceIntegrationTests.GetUserProfile_WithValidAuthentication_ReturnsOk" computerName="ThinkPad-T450" duration="00:00:00.3111503" startTime="2025-07-22T20:30:17.5105950+03:00" endTime="2025-07-22T20:30:17.5105951+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1e277329-a3ff-434e-9343-883ad6ff0dc9" />
    <UnitTestResult executionId="ac063b4f-d578-4ae4-8218-8e0d185068a8" testId="ffc0feb9-e101-9280-39fa-2c80657cdf51" testName="AuthService.Tests.Integration.EndToEndAuthFlowTests.TokenValidation_ExpiredTokenStructure_ShouldBeDetectable" computerName="ThinkPad-T450" duration="00:00:00.0244932" startTime="2025-07-22T20:30:17.4385171+03:00" endTime="2025-07-22T20:30:17.4385173+03:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ac063b4f-d578-4ae4-8218-8e0d185068a8" />
  </Results>
  <TestDefinitions>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientTests.GetProfileAsync_SetsCorrectHeaders" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="7163ecf3-3bef-526f-20a6-3b1a06a49e89">
      <Execution id="2b3a8667-277d-433a-92c0-13ff296c4410" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientTests" name="GetProfileAsync_SetsCorrectHeaders" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.TokenRefreshIntegrationTests.RefreshToken_WithEmptyRefreshToken_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="80a3f711-771a-7680-feaf-0ce341ed3c25">
      <Execution id="61b9a566-9751-48e4-8b8d-80e9560d812c" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.TokenRefreshIntegrationTests" name="RefreshToken_WithEmptyRefreshToken_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SignInWithPhone_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="aae8c18b-e2be-cdf7-7079-9c04515c93e2">
      <Execution id="294eb818-16e4-49d4-a180-1d49c01cb740" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests" name="SignInWithPhone_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.AuthServiceIntegrationTests.GetUserMe_WithValidAuthentication_ReturnsOk" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="a32aabcf-93a8-a4b1-2c17-f956159715e0">
      <Execution id="87e528ea-2a6e-4772-b72e-f3160b9cb7af" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.AuthServiceIntegrationTests" name="GetUserMe_WithValidAuthentication_ReturnsOk" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SendPhoneOtp_WithValidPhoneNumber_ReturnsOkWithSuccessResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="7fc034ae-e9d5-f61f-1692-aa25f95679a8">
      <Execution id="6efb7230-163d-4956-aebf-6e536450d947" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests" name="SendPhoneOtp_WithValidPhoneNumber_ReturnsOkWithSuccessResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.TokenValidationTests.ValidateSupabaseTokenFormat_ShouldMatchExpectedStructure" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="298789a5-58c0-19ca-cab2-72695144cbe0">
      <Execution id="ad397541-c980-49f4-9f6e-eaa5612ef2bc" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.TokenValidationTests" name="ValidateSupabaseTokenFormat_ShouldMatchExpectedStructure" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientGoogleSignInTests.SignInWithGoogleAsync_WithValidCode_ReturnsAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="24b67f52-899d-1fe0-df0c-60ba72860d1f">
      <Execution id="1645cdf2-e779-4331-89c7-f31581a06884" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientGoogleSignInTests" name="SignInWithGoogleAsync_WithValidCode_ReturnsAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.AuthServiceIntegrationTests.GetProfile_WithValidAuthentication_ReturnsOk" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="dcc55313-1af8-6c05-b202-ca2542d5df15">
      <Execution id="957ac510-6840-46b7-87ac-d41c1bb1f4d2" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.AuthServiceIntegrationTests" name="GetProfile_WithValidAuthentication_ReturnsOk" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerTests.GetProfile_WithEmptyUserId_ReturnsUnauthorized" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="3dd4fea6-b413-4334-2bea-1b8bc69be77b">
      <Execution id="7ed2b8be-8046-4087-9e6a-c4fce6140bcb" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerTests" name="GetProfile_WithEmptyUserId_ReturnsUnauthorized" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.TokenRefreshIntegrationTests.RefreshToken_WithInvalidRefreshToken_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="b6e911f7-39f5-0dca-5d60-d342617c3eac">
      <Execution id="7a30c7fb-cd5e-4fca-9509-36ec2f39873b" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.TokenRefreshIntegrationTests" name="RefreshToken_WithInvalidRefreshToken_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerTests.GetProfile_WithWhitespaceUserId_ReturnsUnauthorized" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="c33db39c-f8d8-e5de-3b32-71a91c0121ad">
      <Execution id="96bfe423-c5e8-460c-8857-d5f03de0ae0f" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerTests" name="GetProfile_WithWhitespaceUserId_ReturnsUnauthorized" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.AuthServiceIntegrationTests.GetUserProfile_WithValidAuthentication_ReturnsOk" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="c4a799ef-1926-aa9f-239e-d8bb2edfb77d">
      <Execution id="1e277329-a3ff-434e-9343-883ad6ff0dc9" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.AuthServiceIntegrationTests" name="GetUserProfile_WithValidAuthentication_ReturnsOk" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerTests.RecoverPassword_WithValidEmail_ReturnsOkWithSuccessResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="d9b42c1a-d084-d300-09b0-6f113e3daa15">
      <Execution id="e203147c-65cd-44a6-ab82-2191bbaa668e" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerTests" name="RecoverPassword_WithValidEmail_ReturnsOkWithSuccessResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.AuthServiceIntegrationTests.GetProfile_WithoutAuthentication_ReturnsUnauthorized" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="1dda2492-2a69-841c-2f56-c16a40a30927">
      <Execution id="a21b40df-15c8-4b08-a3dd-df1d2799b55f" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.AuthServiceIntegrationTests" name="GetProfile_WithoutAuthentication_ReturnsUnauthorized" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.EndToEndAuthFlowTests.CompleteAuthFlow_LoginRefreshProfile_ShouldWorkEndToEnd" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="4da301d5-2470-9322-a4aa-afc2e0f53a7a">
      <Execution id="16268f09-0e10-484d-b7fc-3c40ee144b0e" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.EndToEndAuthFlowTests" name="CompleteAuthFlow_LoginRefreshProfile_ShouldWorkEndToEnd" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.TokenValidationTests.ValidateToken_WithWrongIssuer_ShouldFail" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="181a6165-cade-de23-63de-7e7070e84fe0">
      <Execution id="9a981af0-d918-46d0-9530-9b0558e097b4" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.TokenValidationTests" name="ValidateToken_WithWrongIssuer_ShouldFail" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientTests.GetProfileAsync_WhenNoProfileFound_ReturnsNull" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="d007daa9-e0fd-8df4-063d-fca1ce656c34">
      <Execution id="a83cfc1a-0aa0-4907-9e0b-7e4dbe84469a" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientTests" name="GetProfileAsync_WhenNoProfileFound_ReturnsNull" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerTests.SendOtp_WithValidRequest_ReturnsOkWithSuccessResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="6051ab2d-09ef-0f41-0094-e86ec50c0d5f">
      <Execution id="001eec8f-719a-42ae-9234-476cc9efb17d" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerTests" name="SendOtp_WithValidRequest_ReturnsOkWithSuccessResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SendPhoneOtp_WithNullPhoneNumber_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="9794b204-e118-68a7-8104-948da1699e62">
      <Execution id="b8100ddc-3b7f-444d-82c5-ef51c3176261" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests" name="SendPhoneOtp_WithNullPhoneNumber_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientPhoneSignInTests.SendPhoneOtpAsync_WithValidPhoneNumber_ReturnsSuccessResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="9e5d1652-3ab0-6198-6809-7f6cb0f6e4ff">
      <Execution id="2140065a-4e4b-4115-83fc-3fc3bb5d2e9c" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientPhoneSignInTests" name="SendPhoneOtpAsync_WithValidPhoneNumber_ReturnsSuccessResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientAuthTests.RecoverPasswordAsync_WithValidRequest_ReturnsSuccessResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="f8bb0f29-2c5b-9521-2650-3f13c4c7a2af">
      <Execution id="d51b78fa-99d7-49e1-8088-5f4bd929baaa" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientAuthTests" name="RecoverPasswordAsync_WithValidRequest_ReturnsSuccessResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SignInWithPhone_WithEmptyPhoneNumber_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="938e4b5d-a938-ac07-4668-282fb1dcb486">
      <Execution id="d1c83587-0a90-41b1-b369-1e6e23ca3ee9" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests" name="SignInWithPhone_WithEmptyPhoneNumber_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GetGoogleSignInUrl_WithRedirectTo_ReturnsOkWithOAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="20ad6935-f366-3400-b44a-34fa0ce5a57a">
      <Execution id="a33b6329-d46f-4eab-8900-2a0010bb613c" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests" name="GetGoogleSignInUrl_WithRedirectTo_ReturnsOkWithOAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerTests.Login_WithValidRequest_ReturnsOkWithAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="bd6bca39-cf93-5a74-f19f-f5add9766961">
      <Execution id="f9c36708-e500-44df-b7a0-dbba557e54bf" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerTests" name="Login_WithValidRequest_ReturnsOkWithAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerTests.RefreshToken_WithValidRequest_ReturnsOkWithAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="70115306-a79f-e16b-9299-d892c167492b">
      <Execution id="6a1280e0-f625-42be-9d0c-105c14189947" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerTests" name="RefreshToken_WithValidRequest_ReturnsOkWithAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientAuthTests.SignupAsync_WithHttpError_ThrowsHttpRequestException" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="03a04153-62c0-7445-6fc2-bb2aaf958a13">
      <Execution id="29736f69-5e1b-4ebc-91b2-e74c7bb901fd" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientAuthTests" name="SignupAsync_WithHttpError_ThrowsHttpRequestException" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientAuthTests.RefreshTokenAsync_WithValidRequest_ReturnsAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="2c104947-53f9-cbb1-6748-7014c2fd731b">
      <Execution id="81468877-ce65-4337-b651-7a0f457b5186" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientAuthTests" name="RefreshTokenAsync_WithValidRequest_ReturnsAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SendPhoneOtp_WhenSupabaseClientThrowsGenericException_ReturnsInternalServerError" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="823b97b5-c1cc-6dbc-25cc-8ad914b06053">
      <Execution id="02f181f7-d634-4cbe-8e07-495305451eb6" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests" name="SendPhoneOtp_WhenSupabaseClientThrowsGenericException_ReturnsInternalServerError" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientGoogleSignInTests.SignInWithGoogleAsync_WithInvalidCode_ThrowsHttpRequestException" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="cc42a13d-6607-f477-4b2e-c6f3ee233e53">
      <Execution id="bb821071-2949-4644-a519-a068a473e03a" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientGoogleSignInTests" name="SignInWithGoogleAsync_WithInvalidCode_ThrowsHttpRequestException" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.AuthServiceIntegrationTests.GetUserProfile_WithoutAuthentication_ReturnsUnauthorized" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="dd6f1ef8-bcdf-0087-13cc-81d56c9785c7">
      <Execution id="26cc6063-032e-4bf4-a4a6-239901a39e8d" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.AuthServiceIntegrationTests" name="GetUserProfile_WithoutAuthentication_ReturnsUnauthorized" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GoogleCallback_WhenSupabaseClientThrowsGenericException_ReturnsInternalServerError" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="b6b8cab8-7d13-221a-0f9d-88f181ded8fb">
      <Execution id="aae14784-0adf-4053-a579-dd0c256cab57" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests" name="GoogleCallback_WhenSupabaseClientThrowsGenericException_ReturnsInternalServerError" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SignInWithPhone_WithEmptyOtpCode_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="2bc7c15f-1a7b-cfaa-ca12-3b5228df51be">
      <Execution id="2e2ff90b-e0a5-4e97-9aa2-3e0499969a48" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests" name="SignInWithPhone_WithEmptyOtpCode_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GoogleCallback_WithNullCode_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="1e4cb761-0012-56d3-ef03-80b585a86a5a">
      <Execution id="ecdf7b7e-d20a-4539-8e7e-bbac3ea516e5" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests" name="GoogleCallback_WithNullCode_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.TokenValidationTests.ValidateToken_WithWrongAudience_ShouldFail" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="6d86cab8-79cd-6c2d-2860-dbdf7e56def4">
      <Execution id="4043d7d3-c1a7-4a2c-87c0-635e144aeca1" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.TokenValidationTests" name="ValidateToken_WithWrongAudience_ShouldFail" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.TokenRefreshIntegrationTests.RefreshToken_WithValidRefreshToken_ReturnsNewTokens" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="baee4509-f6ed-7368-825a-ebd80d90ef9e">
      <Execution id="b4e18b3e-f572-4e38-9de4-fc193c170c47" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.TokenRefreshIntegrationTests" name="RefreshToken_WithValidRefreshToken_ReturnsNewTokens" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientPhoneSignInTests.SignInWithPhoneAsync_WithInvalidOtp_ThrowsHttpRequestException" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="08c3aac2-17ea-b0dc-fbfd-b158f9f54ca4">
      <Execution id="4e78e249-0346-463d-9963-daa2b8496c77" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientPhoneSignInTests" name="SignInWithPhoneAsync_WithInvalidOtp_ThrowsHttpRequestException" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientPhoneSignInTests.SendPhoneOtpAsync_WithCreateUserFalse_SendsCorrectPayload" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="e4a28ef9-b765-5174-4de1-0ed0d8e7ab74">
      <Execution id="e2432095-9633-4afa-a25c-db80fbb91670" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientPhoneSignInTests" name="SendPhoneOtpAsync_WithCreateUserFalse_SendsCorrectPayload" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientTests.GetProfileAsync_BuildsCorrectUrl" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="8873292f-c258-7eef-1ddc-d40490b6a8f3">
      <Execution id="c2f38066-6dca-42ef-b5bc-b074949e9236" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientTests" name="GetProfileAsync_BuildsCorrectUrl" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.EndToEndAuthFlowTests.TokenValidation_ExpiredTokenStructure_ShouldBeDetectable" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="ffc0feb9-e101-9280-39fa-2c80657cdf51">
      <Execution id="ac063b4f-d578-4ae4-8218-8e0d185068a8" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.EndToEndAuthFlowTests" name="TokenValidation_ExpiredTokenStructure_ShouldBeDetectable" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.AuthServiceIntegrationTests.GetProfile_WhenProfileNotFound_ReturnsNotFound" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="c49fa5e8-ed98-8e4f-3637-9fae643c31fd">
      <Execution id="2217d857-5405-4a55-8b09-bbe3278b8118" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.AuthServiceIntegrationTests" name="GetProfile_WhenProfileNotFound_ReturnsNotFound" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientAuthTests.GetOAuthUrlAsync_WithValidRequest_ReturnsOAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="15f461b5-3c95-d3fe-430d-8c64a68e5627">
      <Execution id="22b61b94-b643-41cc-9fe4-e8fb38a8feea" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientAuthTests" name="GetOAuthUrlAsync_WithValidRequest_ReturnsOAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientGoogleSignInTests.SignInWithGoogleAsync_SendsCorrectRequestPayload" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="eb519c66-e663-263b-a107-dffa550d05e6">
      <Execution id="ce806592-6f5e-4398-944f-dc922e3c917b" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientGoogleSignInTests" name="SignInWithGoogleAsync_SendsCorrectRequestPayload" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientGoogleSignInTests.GetGoogleSignInUrlAsync_WithRedirectTo_ReturnsValidOAuthResponseWithRedirect" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="e6f4805a-99ff-78ac-bf63-1c6532349164">
      <Execution id="9eea3810-4f9d-443d-a4fb-070794f555ce" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientGoogleSignInTests" name="GetGoogleSignInUrlAsync_WithRedirectTo_ReturnsValidOAuthResponseWithRedirect" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GetGoogleSignInUrl_WithoutRedirectTo_ReturnsOkWithOAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="6d0be7e1-5ea1-e731-c019-467debb0369e">
      <Execution id="42b2ac33-ef80-40de-a361-e16b8fe50b0c" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests" name="GetGoogleSignInUrl_WithoutRedirectTo_ReturnsOkWithOAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerTests.GetProfile_WithNoSubClaim_ReturnsUnauthorized" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="72201805-3427-4a50-77a4-4b3ca1683a1e">
      <Execution id="e76e1fb4-3c9e-4d90-8730-1e1e506dd61c" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerTests" name="GetProfile_WithNoSubClaim_ReturnsUnauthorized" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.TokenRefreshIntegrationTests.GeneratedToken_ShouldBeValidForAuthentication" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="10006d2d-43da-e919-7d8d-3618eeec3942">
      <Execution id="5e07640d-7930-42be-97f2-bba54787a44c" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.TokenRefreshIntegrationTests" name="GeneratedToken_ShouldBeValidForAuthentication" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerTests.GetProfile_WhenSupabaseThrowsException_ReturnsInternalServerError" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="8e4017d4-ae1c-287a-6240-9e9b04e85e6a">
      <Execution id="308b3d08-c303-44c6-833b-7b0c1b6f365c" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerTests" name="GetProfile_WhenSupabaseThrowsException_ReturnsInternalServerError" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.EndToEndAuthFlowTests.TokenValidation_GeneratedTokenStructure_ShouldBeValid" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="08f9ef06-54fc-2a42-815a-d5c1222316e8">
      <Execution id="5ece9271-7589-472e-a287-03efc903f5ad" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.EndToEndAuthFlowTests" name="TokenValidation_GeneratedTokenStructure_ShouldBeValid" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientTests.GetProfileAsync_WithValidUserId_ReturnsSupabaseUser" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="6899c30f-b2af-54c4-38c5-ed8ae4c7e1fd">
      <Execution id="7bd94441-01c6-4270-8024-75f4f27ff20c" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientTests" name="GetProfileAsync_WithValidUserId_ReturnsSupabaseUser" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientTests.GetProfileAsync_WhenHttpRequestFails_ThrowsHttpRequestException" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="c2c30624-746b-5b1a-b818-5a035d4d4caa">
      <Execution id="c33453d5-5f2a-422a-b6ab-9ed42675f35d" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientTests" name="GetProfileAsync_WhenHttpRequestFails_ThrowsHttpRequestException" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GoogleCallback_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="0f6ed8c4-f25e-11a7-b9aa-6b2127216740">
      <Execution id="a76d5ffb-bd19-4c66-9ef7-d8d5c20b7d8d" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests" name="GoogleCallback_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientGoogleSignInTests.GetGoogleSignInUrlAsync_WithoutRedirectTo_ReturnsValidOAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="ec16a05a-31c1-ca52-0862-89763bdffbc8">
      <Execution id="f238f2c0-2c65-47ea-bf21-d9b655c2cb11" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientGoogleSignInTests" name="GetGoogleSignInUrlAsync_WithoutRedirectTo_ReturnsValidOAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientPhoneSignInTests.SignInWithPhoneAsync_WithValidCredentials_ReturnsAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="606912b3-3a47-4736-7d13-11fdfa443b80">
      <Execution id="90415933-1853-450f-af00-24d41804a30f" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientPhoneSignInTests" name="SignInWithPhoneAsync_WithValidCredentials_ReturnsAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SendPhoneOtp_WithEmptyPhoneNumber_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="be06c46a-fec5-0bf0-f9d5-071b76119fe9">
      <Execution id="cca32b01-d3f6-4693-b29d-74ab97d557ed" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests" name="SendPhoneOtp_WithEmptyPhoneNumber_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SendPhoneOtp_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="53d0614e-2517-0c33-b00b-924b152e610d">
      <Execution id="cb4fb35d-cd2e-4d12-96e1-894bb67d61ab" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests" name="SendPhoneOtp_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientAuthTests.LoginAsync_WithValidRequest_ReturnsAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="bdd8ecdd-47f6-c264-9b5c-9cbb31aca9c2">
      <Execution id="8306be4d-9a0f-4727-84ec-e13ce6f623ba" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientAuthTests" name="LoginAsync_WithValidRequest_ReturnsAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GetGoogleSignInUrl_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="de3db4b9-15b4-c621-9c9e-87b03edad4a7">
      <Execution id="0fead543-8e79-45a0-8609-58532c3a81c5" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests" name="GetGoogleSignInUrl_WhenSupabaseClientThrowsHttpRequestException_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GoogleCallback_WithValidCode_ReturnsOkWithAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="655fb69b-86c8-f590-19e1-299d9cef0e00">
      <Execution id="f1884c51-02a9-42c1-bf3f-9b7b9b0d8925" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests" name="GoogleCallback_WithValidCode_ReturnsOkWithAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests.GoogleCallback_WithEmptyCode_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="142e657f-2304-4556-4a6b-0942562be76b">
      <Execution id="2b1958ce-1bef-445b-9d1c-4968c686675e" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerGoogleSignInTests" name="GoogleCallback_WithEmptyCode_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerTests.Signup_WithInvalidRequest_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="75a9faa9-1914-1f35-d21e-8bcd8d9525a5">
      <Execution id="9c5a869c-57d8-40a4-a9b7-34e3d73cf4d7" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerTests" name="Signup_WithInvalidRequest_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.TokenValidationTests.GenerateTestToken_ShouldCreateValidJWT" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="231a7169-21b7-3451-bb18-bb1321339130">
      <Execution id="53e587ec-7d80-4b6c-b288-b5a5f8a3ea45" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.TokenValidationTests" name="GenerateTestToken_ShouldCreateValidJWT" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerTests.Signup_WithValidRequest_ReturnsOkWithAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="f3d85af9-364c-6692-a0ce-40ccd2ec6408">
      <Execution id="29c6d617-e343-4882-9f87-f04b622366ee" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerTests" name="Signup_WithValidRequest_ReturnsOkWithAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SignInWithPhone_WithNullValues_ReturnsBadRequest" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="6870dd26-7828-dd97-05c2-7045718815eb">
      <Execution id="4fe6d366-1cb8-4ec1-9b60-60b006c5d6e8" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests" name="SignInWithPhone_WithNullValues_ReturnsBadRequest" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SignInWithPhone_WhenSupabaseClientThrowsGenericException_ReturnsInternalServerError" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="caa420c2-74fb-4520-17c9-3f0d88baa729">
      <Execution id="c4049ecd-e887-4a48-86fc-e13ed82a387d" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests" name="SignInWithPhone_WhenSupabaseClientThrowsGenericException_ReturnsInternalServerError" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientAuthTests.SignupAsync_WithValidRequest_ReturnsAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="a7d182fe-b17a-cf44-4dba-fc0d1bbc44fd">
      <Execution id="0afd00fd-755b-4ed4-b11e-203319c1ba6e" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientAuthTests" name="SignupAsync_WithValidRequest_ReturnsAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerTests.GetProfile_WithNullProfile_ReturnsNotFound" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="0be9325a-a529-bd7a-cf85-b0c9d7feaef7">
      <Execution id="d962df67-76d4-4ec2-8774-02bfd3383c47" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerTests" name="GetProfile_WithNullProfile_ReturnsNotFound" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests.SignInWithPhone_WithValidCredentials_ReturnsOkWithAuthResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="58146934-21e4-220f-6edf-c193e8b1c7b4">
      <Execution id="7586bc63-b86a-4390-86b2-d0e9faa1b06a" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerPhoneSignInTests" name="SignInWithPhone_WithValidCredentials_ReturnsOkWithAuthResponse" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientPhoneSignInTests.SignInWithPhoneAsync_SendsCorrectVerifyPayload" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="3fc93f08-9bb2-d6b8-9d65-f53882199454">
      <Execution id="121c76f5-e9ed-4cdf-b34d-892f1a2fc08e" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientPhoneSignInTests" name="SignInWithPhoneAsync_SendsCorrectVerifyPayload" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientPhoneSignInTests.SendPhoneOtpAsync_WithInvalidPhoneNumber_ThrowsHttpRequestException" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="6da05667-3bd9-a52d-e7d5-5b25bbd25a58">
      <Execution id="8794dc18-1c16-44f2-9d83-952bd8bef5cf" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientPhoneSignInTests" name="SendPhoneOtpAsync_WithInvalidPhoneNumber_ThrowsHttpRequestException" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientTests.GetProfileAsync_WithEmptyUserId_ThrowsArgumentException" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="91f56c33-9900-b7fd-cc2e-3c9e7051dbc1">
      <Execution id="eae77470-52d3-49b6-a882-131611d15e1f" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientTests" name="GetProfileAsync_WithEmptyUserId_ThrowsArgumentException" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Integration.AuthServiceIntegrationTests.HealthCheck_ReturnsOk" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="e305b7b5-2bd4-8b70-a096-1f3a8a1df786">
      <Execution id="c04edee7-7d98-4bf0-8665-9d8754fe8b8e" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Integration.AuthServiceIntegrationTests" name="HealthCheck_ReturnsOk" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Controllers.AuthControllerTests.GetProfile_WithValidUserId_ReturnsOkWithProfile" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="c30ab486-4bb4-f5a4-8b91-a186825a2153">
      <Execution id="3cbe30f1-3c35-4aff-b308-346531b903e9" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Controllers.AuthControllerTests" name="GetProfile_WithValidUserId_ReturnsOkWithProfile" />
    </UnitTest>
    <UnitTest name="AuthService.Tests.Services.SupabaseClientAuthTests.SendOtpAsync_WithValidRequest_ReturnsSuccessResponse" storage="/home/<USER>/devworks/undecprojects/abraapp/tests/auth-service.tests/bin/release/net10.0/auth-service.tests.dll" id="11a5e625-f15c-9949-6c39-5c7006b4a8fc">
      <Execution id="7791edd2-9593-4a99-9973-515b6aa72e3d" />
      <TestMethod codeBase="/home/<USER>/devWorks/undecProjects/abraapp/tests/auth-service.Tests/bin/Release/net10.0/auth-service.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="AuthService.Tests.Services.SupabaseClientAuthTests" name="SendOtpAsync_WithValidRequest_ReturnsSuccessResponse" />
    </UnitTest>
  </TestDefinitions>
  <TestEntries>
    <TestEntry testId="dcc55313-1af8-6c05-b202-ca2542d5df15" executionId="957ac510-6840-46b7-87ac-d41c1bb1f4d2" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bdd8ecdd-47f6-c264-9b5c-9cbb31aca9c2" executionId="8306be4d-9a0f-4727-84ec-e13ce6f623ba" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="58146934-21e4-220f-6edf-c193e8b1c7b4" executionId="7586bc63-b86a-4390-86b2-d0e9faa1b06a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6d0be7e1-5ea1-e731-c019-467debb0369e" executionId="42b2ac33-ef80-40de-a361-e16b8fe50b0c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3fc93f08-9bb2-d6b8-9d65-f53882199454" executionId="121c76f5-e9ed-4cdf-b34d-892f1a2fc08e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1dda2492-2a69-841c-2f56-c16a40a30927" executionId="a21b40df-15c8-4b08-a3dd-df1d2799b55f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4da301d5-2470-9322-a4aa-afc2e0f53a7a" executionId="16268f09-0e10-484d-b7fc-3c40ee144b0e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="15f461b5-3c95-d3fe-430d-8c64a68e5627" executionId="22b61b94-b643-41cc-9fe4-e8fb38a8feea" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="eb519c66-e663-263b-a107-dffa550d05e6" executionId="ce806592-6f5e-4398-944f-dc922e3c917b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="caa420c2-74fb-4520-17c9-3f0d88baa729" executionId="c4049ecd-e887-4a48-86fc-e13ed82a387d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9e5d1652-3ab0-6198-6809-7f6cb0f6e4ff" executionId="2140065a-4e4b-4115-83fc-3fc3bb5d2e9c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b6b8cab8-7d13-221a-0f9d-88f181ded8fb" executionId="aae14784-0adf-4053-a579-dd0c256cab57" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f8bb0f29-2c5b-9521-2650-3f13c4c7a2af" executionId="d51b78fa-99d7-49e1-8088-5f4bd929baaa" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="10006d2d-43da-e919-7d8d-3618eeec3942" executionId="5e07640d-7930-42be-97f2-bba54787a44c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cc42a13d-6607-f477-4b2e-c6f3ee233e53" executionId="bb821071-2949-4644-a519-a068a473e03a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c49fa5e8-ed98-8e4f-3637-9fae643c31fd" executionId="2217d857-5405-4a55-8b09-bbe3278b8118" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b6e911f7-39f5-0dca-5d60-d342617c3eac" executionId="7a30c7fb-cd5e-4fca-9509-36ec2f39873b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="be06c46a-fec5-0bf0-f9d5-071b76119fe9" executionId="cca32b01-d3f6-4693-b29d-74ab97d557ed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7fc034ae-e9d5-f61f-1692-aa25f95679a8" executionId="6efb7230-163d-4956-aebf-6e536450d947" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a7d182fe-b17a-cf44-4dba-fc0d1bbc44fd" executionId="0afd00fd-755b-4ed4-b11e-203319c1ba6e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c30ab486-4bb4-f5a4-8b91-a186825a2153" executionId="3cbe30f1-3c35-4aff-b308-346531b903e9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="938e4b5d-a938-ac07-4668-282fb1dcb486" executionId="d1c83587-0a90-41b1-b369-1e6e23ca3ee9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bd6bca39-cf93-5a74-f19f-f5add9766961" executionId="f9c36708-e500-44df-b7a0-dbba557e54bf" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="baee4509-f6ed-7368-825a-ebd80d90ef9e" executionId="b4e18b3e-f572-4e38-9de4-fc193c170c47" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="dd6f1ef8-bcdf-0087-13cc-81d56c9785c7" executionId="26cc6063-032e-4bf4-a4a6-239901a39e8d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="24b67f52-899d-1fe0-df0c-60ba72860d1f" executionId="1645cdf2-e779-4331-89c7-f31581a06884" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="231a7169-21b7-3451-bb18-bb1321339130" executionId="53e587ec-7d80-4b6c-b288-b5a5f8a3ea45" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="298789a5-58c0-19ca-cab2-72695144cbe0" executionId="ad397541-c980-49f4-9f6e-eaa5612ef2bc" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="142e657f-2304-4556-4a6b-0942562be76b" executionId="2b1958ce-1bef-445b-9d1c-4968c686675e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e6f4805a-99ff-78ac-bf63-1c6532349164" executionId="9eea3810-4f9d-443d-a4fb-070794f555ce" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="75a9faa9-1914-1f35-d21e-8bcd8d9525a5" executionId="9c5a869c-57d8-40a4-a9b7-34e3d73cf4d7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f3d85af9-364c-6692-a0ce-40ccd2ec6408" executionId="29c6d617-e343-4882-9f87-f04b622366ee" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6051ab2d-09ef-0f41-0094-e86ec50c0d5f" executionId="001eec8f-719a-42ae-9234-476cc9efb17d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e305b7b5-2bd4-8b70-a096-1f3a8a1df786" executionId="c04edee7-7d98-4bf0-8665-9d8754fe8b8e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d9b42c1a-d084-d300-09b0-6f113e3daa15" executionId="e203147c-65cd-44a6-ab82-2191bbaa668e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9794b204-e118-68a7-8104-948da1699e62" executionId="b8100ddc-3b7f-444d-82c5-ef51c3176261" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="181a6165-cade-de23-63de-7e7070e84fe0" executionId="9a981af0-d918-46d0-9530-9b0558e097b4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="91f56c33-9900-b7fd-cc2e-3c9e7051dbc1" executionId="eae77470-52d3-49b6-a882-131611d15e1f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="655fb69b-86c8-f590-19e1-299d9cef0e00" executionId="f1884c51-02a9-42c1-bf3f-9b7b9b0d8925" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6d86cab8-79cd-6c2d-2860-dbdf7e56def4" executionId="4043d7d3-c1a7-4a2c-87c0-635e144aeca1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2bc7c15f-1a7b-cfaa-ca12-3b5228df51be" executionId="2e2ff90b-e0a5-4e97-9aa2-3e0499969a48" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3dd4fea6-b413-4334-2bea-1b8bc69be77b" executionId="7ed2b8be-8046-4087-9e6a-c4fce6140bcb" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c33db39c-f8d8-e5de-3b32-71a91c0121ad" executionId="96bfe423-c5e8-460c-8857-d5f03de0ae0f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="de3db4b9-15b4-c621-9c9e-87b03edad4a7" executionId="0fead543-8e79-45a0-8609-58532c3a81c5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="08f9ef06-54fc-2a42-815a-d5c1222316e8" executionId="5ece9271-7589-472e-a287-03efc903f5ad" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="08c3aac2-17ea-b0dc-fbfd-b158f9f54ca4" executionId="4e78e249-0346-463d-9963-daa2b8496c77" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8873292f-c258-7eef-1ddc-d40490b6a8f3" executionId="c2f38066-6dca-42ef-b5bc-b074949e9236" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="11a5e625-f15c-9949-6c39-5c7006b4a8fc" executionId="7791edd2-9593-4a99-9973-515b6aa72e3d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="03a04153-62c0-7445-6fc2-bb2aaf958a13" executionId="29736f69-5e1b-4ebc-91b2-e74c7bb901fd" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6870dd26-7828-dd97-05c2-7045718815eb" executionId="4fe6d366-1cb8-4ec1-9b60-60b006c5d6e8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="70115306-a79f-e16b-9299-d892c167492b" executionId="6a1280e0-f625-42be-9d0c-105c14189947" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="72201805-3427-4a50-77a4-4b3ca1683a1e" executionId="e76e1fb4-3c9e-4d90-8730-1e1e506dd61c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="53d0614e-2517-0c33-b00b-924b152e610d" executionId="cb4fb35d-cd2e-4d12-96e1-894bb67d61ab" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2c104947-53f9-cbb1-6748-7014c2fd731b" executionId="81468877-ce65-4337-b651-7a0f457b5186" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c2c30624-746b-5b1a-b818-5a035d4d4caa" executionId="c33453d5-5f2a-422a-b6ab-9ed42675f35d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e4a28ef9-b765-5174-4de1-0ed0d8e7ab74" executionId="e2432095-9633-4afa-a25c-db80fbb91670" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1e4cb761-0012-56d3-ef03-80b585a86a5a" executionId="ecdf7b7e-d20a-4539-8e7e-bbac3ea516e5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0be9325a-a529-bd7a-cf85-b0c9d7feaef7" executionId="d962df67-76d4-4ec2-8774-02bfd3383c47" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d007daa9-e0fd-8df4-063d-fca1ce656c34" executionId="a83cfc1a-0aa0-4907-9e0b-7e4dbe84469a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8e4017d4-ae1c-287a-6240-9e9b04e85e6a" executionId="308b3d08-c303-44c6-833b-7b0c1b6f365c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="20ad6935-f366-3400-b44a-34fa0ce5a57a" executionId="a33b6329-d46f-4eab-8900-2a0010bb613c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a32aabcf-93a8-a4b1-2c17-f956159715e0" executionId="87e528ea-2a6e-4772-b72e-f3160b9cb7af" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="823b97b5-c1cc-6dbc-25cc-8ad914b06053" executionId="02f181f7-d634-4cbe-8e07-495305451eb6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="606912b3-3a47-4736-7d13-11fdfa443b80" executionId="90415933-1853-450f-af00-24d41804a30f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="aae8c18b-e2be-cdf7-7079-9c04515c93e2" executionId="294eb818-16e4-49d4-a180-1d49c01cb740" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ec16a05a-31c1-ca52-0862-89763bdffbc8" executionId="f238f2c0-2c65-47ea-bf21-d9b655c2cb11" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6899c30f-b2af-54c4-38c5-ed8ae4c7e1fd" executionId="7bd94441-01c6-4270-8024-75f4f27ff20c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="80a3f711-771a-7680-feaf-0ce341ed3c25" executionId="61b9a566-9751-48e4-8b8d-80e9560d812c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0f6ed8c4-f25e-11a7-b9aa-6b2127216740" executionId="a76d5ffb-bd19-4c66-9ef7-d8d5c20b7d8d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7163ecf3-3bef-526f-20a6-3b1a06a49e89" executionId="2b3a8667-277d-433a-92c0-13ff296c4410" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6da05667-3bd9-a52d-e7d5-5b25bbd25a58" executionId="8794dc18-1c16-44f2-9d83-952bd8bef5cf" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c4a799ef-1926-aa9f-239e-d8bb2edfb77d" executionId="1e277329-a3ff-434e-9343-883ad6ff0dc9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ffc0feb9-e101-9280-39fa-2c80657cdf51" executionId="ac063b4f-d578-4ae4-8218-8e0d185068a8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
  </TestEntries>
  <TestLists>
    <TestList name="Results Not in a List" id="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestList name="All Loaded Results" id="19431567-8539-422a-85d7-44ee4e166bda" />
  </TestLists>
  <ResultSummary outcome="Completed">
    <Counters total="73" executed="73" passed="73" failed="0" error="0" timeout="0" aborted="0" inconclusive="0" passedButRunAborted="0" notRunnable="0" notExecuted="0" disconnected="0" warning="0" completed="0" inProgress="0" pending="0" />
    <Output>
      <StdOut>[xUnit.net 00:00:00.01] xUnit.net VSTest Adapter v2.8.2+699d445a1a (64-bit .NET 10.0.0-preview.6.25358.103)
[xUnit.net 00:00:00.26]   Discovering: auth-service.Tests
[xUnit.net 00:00:00.42]   Discovered:  auth-service.Tests
[xUnit.net 00:00:00.43]   Starting:    auth-service.Tests
DEBUG: Using ASP.NET Core native configuration (environment variables + appsettings.json)
DEBUG: Environment: Testing
DEBUG: ✅ Configuration loaded from ASP.NET Core configuration
DEBUG: Using ASP.NET Core native configuration (environment variables + appsettings.json)
DEBUG: Environment: Testing
DEBUG: ✅ Configuration loaded from ASP.NET Core configuration
DEBUG: Using ASP.NET Core native configuration (environment variables + appsettings.json)
DEBUG: Environment: Testing
DEBUG: ✅ Configuration loaded from ASP.NET Core configuration
warn: Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware[3]
      Failed to determine the https port for redirect.
warn: Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware[3]
      Failed to determine the https port for redirect.
warn: Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware[3]
      Failed to determine the https port for redirect.
DEBUG: Using ASP.NET Core native configuration (environment variables + appsettings.json)
DEBUG: Environment: Testing
DEBUG: ✅ Configuration loaded from ASP.NET Core configuration
DEBUG: Using ASP.NET Core native configuration (environment variables + appsettings.json)
DEBUG: Environment: Testing
DEBUG: ✅ Configuration loaded from ASP.NET Core configuration
warn: Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware[3]
      Failed to determine the https port for redirect.
warn: Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware[3]
      Failed to determine the https port for redirect.
DEBUG: Using ASP.NET Core native configuration (environment variables + appsettings.json)
DEBUG: Environment: Testing
DEBUG: ✅ Configuration loaded from ASP.NET Core configuration
DEBUG: Using ASP.NET Core native configuration (environment variables + appsettings.json)
DEBUG: Environment: Testing
DEBUG: ✅ Configuration loaded from ASP.NET Core configuration
warn: Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware[3]
      Failed to determine the https port for redirect.
warn: Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware[3]
      Failed to determine the https port for redirect.
DEBUG: Using ASP.NET Core native configuration (environment variables + appsettings.json)
DEBUG: Environment: Testing
DEBUG: ✅ Configuration loaded from ASP.NET Core configuration
warn: Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware[3]
      Failed to determine the https port for redirect.
info: AuthService.Tests.Integration.TestAuthHandler[12]
      AuthenticationScheme: Test was challenged.
DEBUG: Using ASP.NET Core native configuration (environment variables + appsettings.json)
DEBUG: Environment: Testing
DEBUG: ✅ Configuration loaded from ASP.NET Core configuration
warn: Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware[3]
      Failed to determine the https port for redirect.
info: AuthService.Tests.Integration.TestAuthHandler[12]
      AuthenticationScheme: Test was challenged.
DEBUG: Using ASP.NET Core native configuration (environment variables + appsettings.json)
DEBUG: Environment: Testing
DEBUG: ✅ Configuration loaded from ASP.NET Core configuration
warn: Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware[3]
      Failed to determine the https port for redirect.
DEBUG: Using ASP.NET Core native configuration (environment variables + appsettings.json)
DEBUG: Environment: Testing
DEBUG: ✅ Configuration loaded from ASP.NET Core configuration
warn: Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware[3]
      Failed to determine the https port for redirect.
[xUnit.net 00:00:04.57]   Finished:    auth-service.Tests
</StdOut>
    </Output>
  </ResultSummary>
</TestRun>