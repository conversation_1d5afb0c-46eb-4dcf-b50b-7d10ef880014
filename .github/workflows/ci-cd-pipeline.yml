name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_PREFIX: ${{ github.repository_owner }}/abraapp

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    services:
      ********:
        image: ********:15-alpine
        env:
          POSTGRES_USER: ********
          POSTGRES_PASSWORD: ********
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4
    
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '10.0.x'
        include-prerelease: true
    
    - name: Cache NuGet packages
      uses: actions/cache@v4
      with:
        path: ~/.nuget/packages
        key: ${{ runner.os }}-nuget-${{ hashFiles('**/*.csproj') }}
        restore-keys: |
          ${{ runner.os }}-nuget-
    
    - name: Restore dependencies
      run: dotnet restore
    
    - name: Build solution
      run: dotnet build --no-restore --configuration Release
    
    - name: Run Auth Service Tests
      run: dotnet test tests/auth-service.Tests/auth-service.Tests.csproj --no-build --configuration Release --logger trx --results-directory TestResults/
      env:
        ASPNETCORE_ENVIRONMENT: Testing
        SUPABASE_CONNECTION_STRING: "Host=localhost;Port=5432;Database=testdb;Username=********;Password=********"
    
    - name: Run MarketData Service Tests
      run: dotnet test tests/marketdata-service.Tests/marketdata-service.Tests.csproj --no-build --configuration Release --logger trx --results-directory TestResults/
      env:
        ASPNETCORE_ENVIRONMENT: Testing
        SUPABASE_CONNECTION_STRING: "Host=localhost;Port=5432;Database=testdb;Username=********;Password=********"
        Redis__ConnectionString: "localhost:6379"
    
    - name: Run Assistant Service Tests
      run: dotnet test tests/assistant-service.Tests/assistant-service.Tests.csproj --no-build --configuration Release --logger trx --results-directory TestResults/
      env:
        ASPNETCORE_ENVIRONMENT: Testing
    
    - name: Run Thread Service Tests
      run: dotnet test tests/thread-service.Tests/ --no-build --configuration Release --logger trx --results-directory TestResults/
      env:
        ASPNETCORE_ENVIRONMENT: Testing
        SUPABASE_CONNECTION_STRING: "Host=localhost;Port=5432;Database=testdb;Username=********;Password=********"
    
    - name: Publish Test Results
      uses: dorny/test-reporter@v1
      if: always()
      with:
        name: .NET Tests
        path: TestResults/*.trx
        reporter: dotnet-trx

  build-and-push:
    name: Build and Push Images
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push'
    
    strategy:
      matrix:
        service: [auth-service, assistant-service, marketdata-service, thread-service]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Generate version
      id: version
      run: |
        if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
          VERSION="v$(date +%Y%m%d)-${GITHUB_SHA::8}"
          ENVIRONMENT="production"
        else
          VERSION="v$(date +%Y%m%d)-${GITHUB_SHA::8}-dev"
          ENVIRONMENT="staging"
        fi
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_PREFIX }}-${{ matrix.service }}
        tags: |
          type=raw,value=${{ steps.version.outputs.version }}
          type=raw,value=latest,enable={{is_default_branch}}
          type=raw,value=staging,enable=${{ github.ref == 'refs/heads/develop' }}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./${{ matrix.service }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64
    
    outputs:
      version: ${{ steps.version.outputs.version }}
      environment: ${{ steps.version.outputs.environment }}

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/develop'
    environment: staging

    steps:
    - uses: actions/checkout@v4

    - name: Load environment variables from GitHub secrets
      run: |
        # Export secrets as environment variables for subsequent steps
        echo "SUPABASE_JWT_SECRET=${{ secrets.SUPABASE_JWT_SECRET }}" >> $GITHUB_ENV
        echo "SUPABASE_URL=${{ secrets.SUPABASE_URL }}" >> $GITHUB_ENV
        echo "SUPABASE_PROJECT_ID=${{ secrets.SUPABASE_PROJECT_ID }}" >> $GITHUB_ENV
        echo "SUPABASE_SERVICE_ROLE_KEY=${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" >> $GITHUB_ENV
        echo "SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}" >> $GITHUB_ENV
        echo "SUPABASE_CONNECTION_STRING=${{ secrets.SUPABASE_CONNECTION_STRING }}" >> $GITHUB_ENV
        echo "FINNHUB_API_KEY=${{ secrets.FINNHUB_API_KEY }}" >> $GITHUB_ENV
        echo "POLYGON_API_KEY=${{ secrets.POLYGON_API_KEY }}" >> $GITHUB_ENV
        echo "OLLAMA_URL=${{ secrets.OLLAMA_URL }}" >> $GITHUB_ENV
        echo "REDIS_CONNECTION_STRING=${{ secrets.REDIS_CONNECTION_STRING }}" >> $GITHUB_ENV

    - name: Create Kubernetes secrets from .env
      run: |
        VERSION="${{ needs.build-and-push.outputs.version }}"

        # Create secrets directory
        mkdir -p k8s-secrets

        # Generate auth-service secret
        cat > k8s-secrets/auth-service-secret.yaml << EOF
        apiVersion: v1
        kind: Secret
        metadata:
          name: auth-service-secret
          namespace: abraapi
        type: Opaque
        data:
          SUPABASE_JWT_SECRET: $(echo -n "$SUPABASE_JWT_SECRET" | base64 -w 0)
          SUPABASE_URL: $(echo -n "$SUPABASE_URL" | base64 -w 0)
          SUPABASE_PROJECT_ID: $(echo -n "$SUPABASE_PROJECT_ID" | base64 -w 0)
          SUPABASE_SERVICE_ROLE_KEY: $(echo -n "$SUPABASE_SERVICE_ROLE_KEY" | base64 -w 0)
          SUPABASE_ANON_KEY: $(echo -n "$SUPABASE_ANON_KEY" | base64 -w 0)
        EOF

        # Generate marketdata-service secret
        cat > k8s-secrets/marketdata-service-secret.yaml << EOF
        apiVersion: v1
        kind: Secret
        metadata:
          name: marketdata-service-secret
          namespace: abraapi
        type: Opaque
        data:
          SUPABASE_CONNECTION_STRING: $(echo -n "$SUPABASE_CONNECTION_STRING" | base64 -w 0)
          FINNHUB_API_KEY: $(echo -n "$FINNHUB_API_KEY" | base64 -w 0)
          POLYGON_API_KEY: $(echo -n "$POLYGON_API_KEY" | base64 -w 0)
          SUPABASE_URL: $(echo -n "$SUPABASE_URL" | base64 -w 0)
          SUPABASE_JWT_SECRET: $(echo -n "$SUPABASE_JWT_SECRET" | base64 -w 0)
        EOF

        # Generate thread-service secret
        cat > k8s-secrets/thread-service-secret.yaml << EOF
        apiVersion: v1
        kind: Secret
        metadata:
          name: thread-service-secret
          namespace: abraapi
        type: Opaque
        data:
          SUPABASE_CONNECTION_STRING: $(echo -n "$SUPABASE_CONNECTION_STRING" | base64 -w 0)
          SUPABASE_URL: $(echo -n "$SUPABASE_URL" | base64 -w 0)
          SUPABASE_JWT_SECRET: $(echo -n "$SUPABASE_JWT_SECRET" | base64 -w 0)
        EOF

        # Generate assistant-service secret
        cat > k8s-secrets/assistant-service-secret.yaml << EOF
        apiVersion: v1
        kind: Secret
        metadata:
          name: assistant-service-secret
          namespace: abraapi
        type: Opaque
        data:
          OLLAMA_URL: $(echo -n "$OLLAMA_URL" | base64 -w 0)
        EOF

    - name: Update Kubernetes manifests
      run: |
        VERSION="${{ needs.build-and-push.outputs.version }}"

        # Update image tags in deployment files
        for service in auth-service assistant-service marketdata-service thread-service; do
          sed -i "s|image: ghcr.io/abraapp/${service}:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_PREFIX }}-${service}:${VERSION}|g" ${service}/k8s/deployment.yaml
        done

        # Update Redis connection string in marketdata-service config
        sed -i "s|Redis__ConnectionString:.*|Redis__ConnectionString: \"$REDIS_CONNECTION_STRING\"|g" marketdata-service/k8s/config.yaml

    - name: Setup kubectl for K3s
      run: |
        # Create .kube directory
        mkdir -p $HOME/.kube

        # Decode and setup K3s kubeconfig
        echo "${{ secrets.KUBE_CONFIG_K3S }}" | base64 -d > $HOME/.kube/config
        chmod 600 $HOME/.kube/config

        # Verify connection
        kubectl cluster-info
        kubectl get nodes

    - name: Deploy to K3s Staging
      run: |
        echo "🚀 Deploying to K3s staging with version ${{ needs.build-and-push.outputs.version }}"

        # Apply infrastructure first (namespaces, ********, redis)
        echo "📦 Deploying infrastructure..."
        kubectl apply -f infrastructure/k8s/

        # Wait for infrastructure to be ready
        echo "⏳ Waiting for infrastructure..."
        kubectl wait --for=condition=ready pod -l app=******** -n abraapi-infrastructure --timeout=300s || true
        kubectl wait --for=condition=ready pod -l app=redis -n abraapi-infrastructure --timeout=300s || true

        # Apply generated secrets
        echo "🔐 Creating secrets..."
        kubectl apply -f k8s-secrets/

        # Apply service configurations and deployments
        echo "⚙️ Deploying services..."
        kubectl apply -f auth-service/k8s/
        kubectl apply -f assistant-service/k8s/
        kubectl apply -f marketdata-service/k8s/
        kubectl apply -f thread-service/k8s/

        # Wait for deployments to be ready
        echo "⏳ Waiting for services to be ready..."
        kubectl rollout status deployment/auth-service -n abraapi --timeout=300s
        kubectl rollout status deployment/assistant-service -n abraapi --timeout=300s
        kubectl rollout status deployment/marketdata-service -n abraapi --timeout=300s
        kubectl rollout status deployment/thread-service -n abraapi --timeout=300s

        # Show deployment status
        echo "📊 Deployment Status:"
        kubectl get pods -n abraapi
        kubectl get services -n abraapi

        echo "✅ K3s staging deployment completed!"

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - uses: actions/checkout@v4

    - name: Load environment variables from GitHub secrets
      run: |
        # Export secrets as environment variables for subsequent steps
        echo "SUPABASE_JWT_SECRET=${{ secrets.SUPABASE_JWT_SECRET }}" >> $GITHUB_ENV
        echo "SUPABASE_URL=${{ secrets.SUPABASE_URL }}" >> $GITHUB_ENV
        echo "SUPABASE_PROJECT_ID=${{ secrets.SUPABASE_PROJECT_ID }}" >> $GITHUB_ENV
        echo "SUPABASE_SERVICE_ROLE_KEY=${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" >> $GITHUB_ENV
        echo "SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}" >> $GITHUB_ENV
        echo "SUPABASE_CONNECTION_STRING=${{ secrets.SUPABASE_CONNECTION_STRING }}" >> $GITHUB_ENV
        echo "FINNHUB_API_KEY=${{ secrets.FINNHUB_API_KEY }}" >> $GITHUB_ENV
        echo "POLYGON_API_KEY=${{ secrets.POLYGON_API_KEY }}" >> $GITHUB_ENV
        echo "OLLAMA_URL=${{ secrets.OLLAMA_URL }}" >> $GITHUB_ENV
        echo "REDIS_CONNECTION_STRING=${{ secrets.REDIS_CONNECTION_STRING }}" >> $GITHUB_ENV

    - name: Create Kubernetes secrets from .env
      run: |
        VERSION="${{ needs.build-and-push.outputs.version }}"

        # Create secrets directory
        mkdir -p k8s-secrets

        # Generate auth-service secret
        cat > k8s-secrets/auth-service-secret.yaml << EOF
        apiVersion: v1
        kind: Secret
        metadata:
          name: auth-service-secret
          namespace: abraapi
        type: Opaque
        data:
          SUPABASE_JWT_SECRET: $(echo -n "$SUPABASE_JWT_SECRET" | base64 -w 0)
          SUPABASE_URL: $(echo -n "$SUPABASE_URL" | base64 -w 0)
          SUPABASE_PROJECT_ID: $(echo -n "$SUPABASE_PROJECT_ID" | base64 -w 0)
          SUPABASE_SERVICE_ROLE_KEY: $(echo -n "$SUPABASE_SERVICE_ROLE_KEY" | base64 -w 0)
          SUPABASE_ANON_KEY: $(echo -n "$SUPABASE_ANON_KEY" | base64 -w 0)
        EOF

        # Generate marketdata-service secret
        cat > k8s-secrets/marketdata-service-secret.yaml << EOF
        apiVersion: v1
        kind: Secret
        metadata:
          name: marketdata-service-secret
          namespace: abraapi
        type: Opaque
        data:
          SUPABASE_CONNECTION_STRING: $(echo -n "$SUPABASE_CONNECTION_STRING" | base64 -w 0)
          FINNHUB_API_KEY: $(echo -n "$FINNHUB_API_KEY" | base64 -w 0)
          POLYGON_API_KEY: $(echo -n "$POLYGON_API_KEY" | base64 -w 0)
          SUPABASE_URL: $(echo -n "$SUPABASE_URL" | base64 -w 0)
          SUPABASE_JWT_SECRET: $(echo -n "$SUPABASE_JWT_SECRET" | base64 -w 0)
        EOF

        # Generate thread-service secret
        cat > k8s-secrets/thread-service-secret.yaml << EOF
        apiVersion: v1
        kind: Secret
        metadata:
          name: thread-service-secret
          namespace: abraapi
        type: Opaque
        data:
          SUPABASE_CONNECTION_STRING: $(echo -n "$SUPABASE_CONNECTION_STRING" | base64 -w 0)
          SUPABASE_URL: $(echo -n "$SUPABASE_URL" | base64 -w 0)
          SUPABASE_JWT_SECRET: $(echo -n "$SUPABASE_JWT_SECRET" | base64 -w 0)
        EOF

        # Generate assistant-service secret
        cat > k8s-secrets/assistant-service-secret.yaml << EOF
        apiVersion: v1
        kind: Secret
        metadata:
          name: assistant-service-secret
          namespace: abraapi
        type: Opaque
        data:
          OLLAMA_URL: $(echo -n "$OLLAMA_URL" | base64 -w 0)
        EOF

    - name: Update Kubernetes manifests
      run: |
        VERSION="${{ needs.build-and-push.outputs.version }}"

        # Update image tags in deployment files
        for service in auth-service assistant-service marketdata-service thread-service; do
          sed -i "s|image: ghcr.io/abraapp/${service}:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_PREFIX }}-${service}:${VERSION}|g" ${service}/k8s/deployment.yaml
        done

        # Update Redis connection string in marketdata-service config
        sed -i "s|Redis__ConnectionString:.*|Redis__ConnectionString: \"$REDIS_CONNECTION_STRING\"|g" marketdata-service/k8s/config.yaml

    - name: Setup kubectl for K3s
      run: |
        # Create .kube directory
        mkdir -p $HOME/.kube

        # Decode and setup K3s kubeconfig
        echo "${{ secrets.KUBE_CONFIG_K3S }}" | base64 -d > $HOME/.kube/config
        chmod 600 $HOME/.kube/config

        # Verify connection
        kubectl cluster-info
        kubectl get nodes

    - name: Deploy to K3s Production
      run: |
        echo "🚀 Deploying to K3s production with version ${{ needs.build-and-push.outputs.version }}"

        # Apply infrastructure first (namespaces, ********, redis)
        echo "📦 Deploying infrastructure..."
        kubectl apply -f infrastructure/k8s/

        # Wait for infrastructure to be ready
        echo "⏳ Waiting for infrastructure..."
        kubectl wait --for=condition=ready pod -l app=******** -n abraapi-infrastructure --timeout=300s || true
        kubectl wait --for=condition=ready pod -l app=redis -n abraapi-infrastructure --timeout=300s || true

        # Apply generated secrets
        echo "🔐 Creating secrets..."
        kubectl apply -f k8s-secrets/

        # Apply service configurations and deployments
        echo "⚙️ Deploying services..."
        kubectl apply -f auth-service/k8s/
        kubectl apply -f assistant-service/k8s/
        kubectl apply -f marketdata-service/k8s/
        kubectl apply -f thread-service/k8s/

        # Wait for deployments to be ready
        echo "⏳ Waiting for services to be ready..."
        kubectl rollout status deployment/auth-service -n abraapi --timeout=300s
        kubectl rollout status deployment/assistant-service -n abraapi --timeout=300s
        kubectl rollout status deployment/marketdata-service -n abraapi --timeout=300s
        kubectl rollout status deployment/thread-service -n abraapi --timeout=300s

        # Show deployment status
        echo "📊 Deployment Status:"
        kubectl get pods -n abraapi
        kubectl get services -n abraapi

        echo "✅ K3s production deployment completed!"
