{"runtimeTarget": {"name": ".NETCoreApp,Version=v10.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v10.0": {"AuthService/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.11", "Microsoft.AspNetCore.OpenApi": "8.0.0", "Microsoft.IdentityModel.Tokens": "8.12.1", "Newtonsoft.Json": "13.0.3", "Supabase": "1.1.1", "Swashbuckle.AspNetCore": "6.5.0", "System.IdentityModel.Tokens.Jwt": "8.12.1"}, "runtime": {"AuthService.dll": {}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.11": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "8.0.11.0", "fileVersion": "8.0.1124.52116"}}}, "Microsoft.AspNetCore.OpenApi/8.0.0": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53112"}}}, "Microsoft.IdentityModel.Abstractions/8.12.1": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "8.12.1.0", "fileVersion": "8.12.1.60617"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.12.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.12.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "8.12.1.0", "fileVersion": "8.12.1.60617"}}}, "Microsoft.IdentityModel.Logging/8.12.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.12.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "8.12.1.0", "fileVersion": "8.12.1.60617"}}}, "Microsoft.IdentityModel.Protocols/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.12.1", "Microsoft.IdentityModel.Tokens": "8.12.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.1.2", "System.IdentityModel.Tokens.Jwt": "8.12.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Tokens/8.12.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.12.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "8.12.1.0", "fileVersion": "8.12.1.60617"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}}, "Microsoft.OpenApi/1.4.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.4.3.0", "fileVersion": "1.4.3.0"}}}, "MimeMapping/3.0.1": {"runtime": {"lib/netstandard2.0/MimeMapping.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.1.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Supabase/1.1.1": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Supabase.Core": "1.0.0", "Supabase.Functions": "2.0.0", "Supabase.Gotrue": "6.0.3", "Supabase.Postgrest": "4.0.3", "Supabase.Realtime": "7.0.2", "Supabase.Storage": "2.0.2"}, "runtime": {"lib/netstandard2.1/Supabase.dll": {"assemblyVersion": "1.1.1.0", "fileVersion": "1.1.1.0"}}}, "Supabase.Core/1.0.0": {"runtime": {"lib/netstandard2.0/Supabase.Core.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Supabase.Functions/2.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Supabase.Core": "1.0.0"}, "runtime": {"lib/netstandard2.0/Supabase.Functions.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "Supabase.Gotrue/6.0.3": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Supabase.Core": "1.0.0", "System.IdentityModel.Tokens.Jwt": "8.12.1"}, "runtime": {"lib/netstandard2.1/Supabase.Gotrue.dll": {"assemblyVersion": "6.0.3.0", "fileVersion": "6.0.3.0"}}}, "Supabase.Postgrest/4.0.3": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Supabase.Core": "1.0.0"}, "runtime": {"lib/netstandard2.0/Supabase.Postgrest.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.0.3.0"}}}, "Supabase.Realtime/7.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Supabase.Core": "1.0.0", "Supabase.Postgrest": "4.0.3", "Websocket.Client": "5.1.1"}, "runtime": {"lib/netstandard2.1/Supabase.Realtime.dll": {"assemblyVersion": "7.0.2.0", "fileVersion": "7.0.2.0"}}}, "Supabase.Storage/2.0.2": {"dependencies": {"MimeMapping": "3.0.1", "Newtonsoft.Json": "13.0.3", "Supabase.Core": "1.0.0"}, "runtime": {"lib/netstandard2.0/Supabase.Storage.dll": {"assemblyVersion": "2.0.2.0", "fileVersion": "2.0.2.0"}}}, "Swashbuckle.AspNetCore/6.5.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.5.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.5.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.5.0"}}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "6.5.0.0", "fileVersion": "6.5.0.0"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.5.0"}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "6.5.0.0", "fileVersion": "6.5.0.0"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "6.5.0.0", "fileVersion": "6.5.0.0"}}}, "System.IdentityModel.Tokens.Jwt/8.12.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.12.1", "Microsoft.IdentityModel.Tokens": "8.12.1"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "8.12.1.0", "fileVersion": "8.12.1.60617"}}}, "System.Reactive/6.0.0": {"runtime": {"lib/net6.0/System.Reactive.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.1"}}}, "Websocket.Client/5.1.1": {"dependencies": {"Microsoft.IO.RecyclableMemoryStream": "3.0.0", "System.Reactive": "6.0.0"}, "runtime": {"lib/net8.0/Websocket.Client.dll": {"assemblyVersion": "5.1.1.0", "fileVersion": "5.1.1.0"}}}}}, "libraries": {"AuthService/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-9KhRuywosM24BPf1R5erwsvIkpRUu1+btVyOPlM3JgrhFVP4pq5Fuzi3vjP01OHXfbCtNhWa+HGkZeqaWdcO5w==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.11", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T4mwMvPSOYAp+KeQ4xO8H2rxpiOMJ9W/7yBBkUTMp96AHtGlPN4s7hbax2tM61LxTY775JKL4fiv5grn41EHXw==", "path": "microsoft.aspnetcore.openapi/8.0.0", "hashPath": "microsoft.aspnetcore.openapi.8.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-JzWhET0VOCORyJbqDc1Wtdl8Q/l+I1MjFB0I/Jko+Ma691JZll8X6o9XwZtUce8FkqGuV4uY4/V1808XZOpDVg==", "path": "microsoft.identitymodel.abstractions/8.12.1", "hashPath": "microsoft.identitymodel.abstractions.8.12.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-imi3xiRLzzKxN4m1aR9Z2X8GUmNsVH7GLA6AkwYStNnh3UzupFtHEEVk3GK1fCvnYdRbpnCGNYY6WQb9AfDAKg==", "path": "microsoft.identitymodel.jsonwebtokens/8.12.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.12.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-39HjVkU7Voe2jRLmORRB5PoTmta1ZPKzUZCc6ldlNlLzdx+um0+fAnvfk05LUQPrNxpvb5ZoqF00SrNvyO2Fzg==", "path": "microsoft.identitymodel.logging/8.12.1", "hashPath": "microsoft.identitymodel.logging.8.12.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-SydLwMRFx6EHPWJ+N6+MVaoArN1Htt92b935O3RUWPY1yUF63zEjvd3lBu79eWdZUwedP8TN2I5V9T3nackvIQ==", "path": "microsoft.identitymodel.protocols/7.1.2", "hashPath": "microsoft.identitymodel.protocols.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/7.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-DzgPEABn3eZmIk4lhov0QPcoHkIbnAfgkyDPM7uGuWDHeockR9DdqNCD9Zy30hPfExu5VhbOXn9oPRi+tFUhEQ==", "path": "microsoft.identitymodel.tokens/8.12.1", "hashPath": "microsoft.identitymodel.tokens.8.12.1.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-irv0HuqoH8Ig5i2fO+8dmDNdFdsrO+DoQcedwIlb810qpZHBNQHZLW7C/AHBQDgLLpw2T96vmMAy/aE4Yj55Sg==", "path": "microsoft.io.recyclablememorystream/3.0.0", "hashPath": "microsoft.io.recyclablememorystream.3.0.0.nupkg.sha512"}, "Microsoft.OpenApi/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-rURwggB+QZYcSVbDr7HSdhw/FELvMlriW10OeOzjPT7pstefMo7IThhtNtDudxbXhW+lj0NfX72Ka5EDsG8x6w==", "path": "microsoft.openapi/1.4.3", "hashPath": "microsoft.openapi.1.4.3.nupkg.sha512"}, "MimeMapping/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-lhYcUVnWKaqrboAwi05YLCx3wdluM9Sr1Mv5Emhgc8c8yNVvdiSEnQJMdDvgb4grlYTaOmbnhYaezoeateX95w==", "path": "mimemapping/3.0.1", "hashPath": "mimemapping.3.0.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Supabase/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-LW9O05IiZBW3YUDT/gndPKvzT3I7PVgx0j87+AdJu7mT42m7oh2nOnjuDR9pU0E9FE2Ke/QGovrH/u5OjGn7lg==", "path": "supabase/1.1.1", "hashPath": "supabase.1.1.1.nupkg.sha512"}, "Supabase.Core/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fJK3Kfq1alw53AGWHBr+dhPu+BUR5dKuBjGhcxrFRVdsFFFWSD5iPIdTYi0CUQDA2b1OjGudYL1xd51yp4hU9Q==", "path": "supabase.core/1.0.0", "hashPath": "supabase.core.1.0.0.nupkg.sha512"}, "Supabase.Functions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rc6zlo6XFkQw/B9fEgwpXHtmNPrnnHmtc96PTbviBJ9lzqPT/Un/G3V9MltlORtnG2RLkLvvS/t2AXAO8m2SCQ==", "path": "supabase.functions/2.0.0", "hashPath": "supabase.functions.2.0.0.nupkg.sha512"}, "Supabase.Gotrue/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-8dkg000ib95bJm0ffDPUrALrefBHrkiqcolNjuUEStF2TeytPbV+OtOUJAMBOqZ2f1nsCY61Ck/7NOmOyGteDw==", "path": "supabase.gotrue/6.0.3", "hashPath": "supabase.gotrue.6.0.3.nupkg.sha512"}, "Supabase.Postgrest/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-hpDIh+E5bDgZiHMuM6l+RDm9WWlt/T3fXXFQoqzlsrJFesFMNzkK9feVBG5egJyL/OlwmxuLsnPkR7O+bV3Vcw==", "path": "supabase.postgrest/4.0.3", "hashPath": "supabase.postgrest.4.0.3.nupkg.sha512"}, "Supabase.Realtime/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-9nwlR9RR+uyD63VUaiqAPsd76NaeM6ORAHq7otc4G34RzBtf6aqjPg9IF6d1DGIk78/lJP31+D4NJmQhl3PkJQ==", "path": "supabase.realtime/7.0.2", "hashPath": "supabase.realtime.7.0.2.nupkg.sha512"}, "Supabase.Storage/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-YKxgwVgLjxi32ze29FT8aSLifiTVSZSxSyI9taOewp6wuxh+aMIPLKWtGEhFhegLv2iFwGcZ3ybMDkCi6RB7Rw==", "path": "supabase.storage/2.0.2", "hashPath": "supabase.storage.2.0.2.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-FK05XokgjgwlCI6wCT+D4/abtQkL1X1/B9Oas6uIwHFmYrIO9WUD5aLC9IzMs9GnHfUXOtXZ2S43gN1mhs5+aA==", "path": "swashbuckle.aspnetcore/6.5.0", "hashPath": "swashbuckle.aspnetcore.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-XWmCmqyFmoItXKFsQSwQbEAsjDKcxlNf1l+/Ki42hcb6LjKL8m5Db69OTvz5vLonMSRntYO1XLqz0OP+n3vKnA==", "path": "swashbuckle.aspnetcore.swagger/6.5.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/qW8Qdg9OEs7V013tt+94OdPxbRdbhcEbw4NiwGvf4YBcfhL/y7qp/Mjv/cENsQ2L3NqJ2AOu94weBy/h4KvA==", "path": "swashbuckle.aspnetcore.swaggergen/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-OvbvxX+wL8skxTBttcBsVxdh73Fag4xwqEU2edh4JMn7Ws/xJHnY/JB1e9RoCb6XpDxUF3hD9A0Z1lEUx40Pfw==", "path": "swashbuckle.aspnetcore.swaggerui/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-jlYdVOJrdeyD80ppEqKJ8BhJdrV3MjeA+seURdhC0DnD41GyUA9Ik+P7Sb571ufVVCYIx93GjeqVvY3QyQxZAA==", "path": "system.identitymodel.tokens.jwt/8.12.1", "hashPath": "system.identitymodel.tokens.jwt.8.12.1.nupkg.sha512"}, "System.Reactive/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-31kfaW4ZupZzPsI5PVe77VhnvFF55qgma7KZr/E0iFTs6fmdhhG8j0mgEx620iLTey1EynOkEfnyTjtNEpJzGw==", "path": "system.reactive/6.0.0", "hashPath": "system.reactive.6.0.0.nupkg.sha512"}, "Websocket.Client/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-VpQV6b8HRnw6bFFIPTOOMtOxba3/viH9K2U2LdOYNjQ2b2HrLHxjodmJr3nPwyNSrtrRPr1RDwOMJ5qqlPnCVg==", "path": "websocket.client/5.1.1", "hashPath": "websocket.client.5.1.1.nupkg.sha512"}}}