using Microsoft.AspNetCore.Mvc;
using AssistantService.Models;
using AssistantService.Services;

namespace AssistantService.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AssistantController(IOllamaClient ollama, IPromptInjectionDetector injectionDetector, IContentFilter contentFilter) : ControllerBase
{
    private readonly IOllamaClient _ollama = ollama;
    private readonly IPromptInjectionDetector _injectionDetector = injectionDetector;
    private readonly IContentFilter _contentFilter = contentFilter;

    [HttpPost("ask")]
    public async Task<ActionResult<AskResponse>> Ask([FromBody] AskRequest request)
    {
        // Validate input
        if (request == null)
        {
            return BadRequest("Request cannot be empty");
        }

        // Check for prompt injection attacks
        var injectionResult = _injectionDetector.AnalyzeInput(request.Prompt);

        if (injectionResult.RiskLevel == RiskLevel.Critical)
        {
            return BadRequest("Request blocked due to security concerns");
        }

        if (injectionResult.RiskLevel == RiskLevel.High)
        {
            return BadRequest("Request contains potentially harmful content");
        }

        // Apply content filtering
        var contentResult = _contentFilter.FilterContent(injectionResult.SanitizedInput ?? request.Prompt);

        if (!contentResult.IsContentSafe)
        {
            return BadRequest($"Content policy violation: {contentResult.Reason}");
        }

        // Use filtered content
        var sanitizedPrompt = contentResult.FilteredContent ?? string.Empty;

        if (string.IsNullOrWhiteSpace(sanitizedPrompt))
        {
            return BadRequest("Prompt cannot be empty");
        }

        if (sanitizedPrompt.Length > 4000)
        {
            return BadRequest("Prompt is too long (max 4000 characters)");
        }

        try
        {
            var reply = await _ollama.AskAsync(sanitizedPrompt);
            return Ok(new AskResponse { Reply = reply });
        }
        catch (InvalidOperationException ex)
        {
            return StatusCode(503, ex.Message);
        }
        catch (Exception)
        {
            return StatusCode(500, "Something went wrong. Please try again.");
        }
    }


}
