{"runtimeTarget": {"name": ".NETCoreApp,Version=v10.0", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "DEBUG", "NET", "NET10_0", "NETCOREAPP", "NET5_0_OR_GREATER", "NET6_0_OR_GREATER", "NET7_0_OR_GREATER", "NET8_0_OR_GREATER", "NET9_0_OR_GREATER", "NET10_0_OR_GREATER", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "languageVersion": "13.0", "platform": "", "allowUnsafe": false, "warningsAsErrors": false, "optimize": false, "keyFile": "", "emitEntryPoint": true, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETCoreApp,Version=v10.0": {"auth-service.Tests/1.0.0": {"dependencies": {"AuthService": "1.0.0", "FluentAssertions": "6.12.2", "Microsoft.AspNetCore.Mvc.Testing": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Http": "9.0.0", "Microsoft.IdentityModel.Tokens": "8.12.1", "Microsoft.NET.Test.Sdk": "17.12.0", "Moq": "4.20.72", "System.IdentityModel.Tokens.Jwt": "8.12.1", "coverlet.collector": "6.0.2", "xunit": "2.9.2", "xunit.runner.visualstudio": "2.8.2", "Microsoft.AspNetCore.Antiforgery": "10.0.0.0", "Microsoft.AspNetCore.Authentication.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Authentication.BearerToken": "10.0.0.0", "Microsoft.AspNetCore.Authentication.Cookies": "10.0.0.0", "Microsoft.AspNetCore.Authentication.Core": "10.0.0.0", "Microsoft.AspNetCore.Authentication": "10.0.0.0", "Microsoft.AspNetCore.Authentication.OAuth": "10.0.0.0", "Microsoft.AspNetCore.Authorization": "10.0.0.0", "Microsoft.AspNetCore.Authorization.Policy": "10.0.0.0", "Microsoft.AspNetCore.Components.Authorization": "10.0.0.0", "Microsoft.AspNetCore.Components": "10.0.0.0", "Microsoft.AspNetCore.Components.Endpoints": "10.0.0.0", "Microsoft.AspNetCore.Components.Forms": "10.0.0.0", "Microsoft.AspNetCore.Components.Server": "10.0.0.0", "Microsoft.AspNetCore.Components.Web": "10.0.0.0", "Microsoft.AspNetCore.Connections.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.CookiePolicy": "10.0.0.0", "Microsoft.AspNetCore.Cors": "10.0.0.0", "Microsoft.AspNetCore.Cryptography.Internal": "10.0.0.0", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "10.0.0.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.DataProtection": "10.0.0.0", "Microsoft.AspNetCore.DataProtection.Extensions": "10.0.0.0", "Microsoft.AspNetCore.Diagnostics.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Diagnostics": "10.0.0.0", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "10.0.0.0", "Microsoft.AspNetCore": "10.0.0.0", "Microsoft.AspNetCore.HostFiltering": "10.0.0.0", "Microsoft.AspNetCore.Hosting.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Hosting": "10.0.0.0", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Html.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Http.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Http.Connections.Common": "10.0.0.0", "Microsoft.AspNetCore.Http.Connections": "10.0.0.0", "Microsoft.AspNetCore.Http": "10.0.0.0", "Microsoft.AspNetCore.Http.Extensions": "10.0.0.0", "Microsoft.AspNetCore.Http.Features": "10.0.0.0", "Microsoft.AspNetCore.Http.Results": "10.0.0.0", "Microsoft.AspNetCore.HttpLogging": "10.0.0.0", "Microsoft.AspNetCore.HttpOverrides": "10.0.0.0", "Microsoft.AspNetCore.HttpsPolicy": "10.0.0.0", "Microsoft.AspNetCore.Identity": "10.0.0.0", "Microsoft.AspNetCore.Localization": "10.0.0.0", "Microsoft.AspNetCore.Localization.Routing": "10.0.0.0", "Microsoft.AspNetCore.Metadata": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Mvc.ApiExplorer": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Core": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Cors": "10.0.0.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "10.0.0.0", "Microsoft.AspNetCore.Mvc": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Localization": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Razor": "10.0.0.0", "Microsoft.AspNetCore.Mvc.RazorPages": "10.0.0.0", "Microsoft.AspNetCore.Mvc.TagHelpers": "10.0.0.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "10.0.0.0", "Microsoft.AspNetCore.OutputCaching": "10.0.0.0", "Microsoft.AspNetCore.RateLimiting": "10.0.0.0", "Microsoft.AspNetCore.Razor": "10.0.0.0", "Microsoft.AspNetCore.Razor.Runtime": "10.0.0.0", "Microsoft.AspNetCore.RequestDecompression": "10.0.0.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.ResponseCaching": "10.0.0.0", "Microsoft.AspNetCore.ResponseCompression": "10.0.0.0", "Microsoft.AspNetCore.Rewrite": "10.0.0.0", "Microsoft.AspNetCore.Routing.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Routing": "10.0.0.0", "Microsoft.AspNetCore.Server.HttpSys": "10.0.0.0", "Microsoft.AspNetCore.Server.IIS": "10.0.0.0", "Microsoft.AspNetCore.Server.IISIntegration": "10.0.0.0", "Microsoft.AspNetCore.Server.Kestrel.Core": "10.0.0.0", "Microsoft.AspNetCore.Server.Kestrel": "10.0.0.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "10.0.0.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "10.0.0.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "10.0.0.0", "Microsoft.AspNetCore.Session": "10.0.0.0", "Microsoft.AspNetCore.SignalR.Common": "10.0.0.0", "Microsoft.AspNetCore.SignalR.Core": "10.0.0.0", "Microsoft.AspNetCore.SignalR": "10.0.0.0", "Microsoft.AspNetCore.SignalR.Protocols.Json": "10.0.0.0", "Microsoft.AspNetCore.StaticAssets": "10.0.0.0", "Microsoft.AspNetCore.StaticFiles": "10.0.0.0", "Microsoft.AspNetCore.WebSockets": "10.0.0.0", "Microsoft.AspNetCore.WebUtilities": "10.0.0.0", "Microsoft.CSharp": "10.0.0.0", "Microsoft.Extensions.Caching.Abstractions": "10.0.0.0", "Microsoft.Extensions.Caching.Memory": "10.0.0.0", "Microsoft.Extensions.Configuration.Abstractions.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.Binder.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.CommandLine.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.FileExtensions.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.Ini": "10.0.0.0", "Microsoft.Extensions.Configuration.Json.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.KeyPerFile": "10.0.0.0", "Microsoft.Extensions.Configuration.UserSecrets.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.Xml": "10.0.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions.Reference": "10.0.0.0", "Microsoft.Extensions.DependencyInjection.Reference": "10.0.0.0", "Microsoft.Extensions.Diagnostics.Abstractions.Reference": "10.0.0.0", "Microsoft.Extensions.Diagnostics.Reference": "10.0.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "10.0.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "10.0.0.0", "Microsoft.Extensions.Features": "10.0.0.0", "Microsoft.Extensions.FileProviders.Abstractions.Reference": "10.0.0.0", "Microsoft.Extensions.FileProviders.Composite": "10.0.0.0", "Microsoft.Extensions.FileProviders.Embedded": "10.0.0.0", "Microsoft.Extensions.FileProviders.Physical.Reference": "10.0.0.0", "Microsoft.Extensions.FileSystemGlobbing.Reference": "10.0.0.0", "Microsoft.Extensions.Hosting.Abstractions.Reference": "10.0.0.0", "Microsoft.Extensions.Hosting.Reference": "10.0.0.0", "Microsoft.Extensions.Http.Reference": "10.0.0.0", "Microsoft.Extensions.Identity.Core": "10.0.0.0", "Microsoft.Extensions.Identity.Stores": "10.0.0.0", "Microsoft.Extensions.Localization.Abstractions": "10.0.0.0", "Microsoft.Extensions.Localization": "10.0.0.0", "Microsoft.Extensions.Logging.Abstractions.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.Configuration.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.Console.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.Debug.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.EventLog.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.EventSource.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.TraceSource": "10.0.0.0", "Microsoft.Extensions.ObjectPool": "10.0.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions.Reference": "10.0.0.0", "Microsoft.Extensions.Options.DataAnnotations": "10.0.0.0", "Microsoft.Extensions.Options.Reference": "10.0.0.0", "Microsoft.Extensions.Primitives.Reference": "10.0.0.0", "Microsoft.Extensions.Validation": "10.0.0.0", "Microsoft.Extensions.WebEncoders": "10.0.0.0", "Microsoft.JSInterop": "10.0.0.0", "Microsoft.Net.Http.Headers": "10.0.0.0", "Microsoft.VisualBasic.Core": "********", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives": "10.0.0.0", "Microsoft.Win32.Registry": "10.0.0.0", "mscorlib": "*******", "netstandard": "2.1.0.0", "System.AppContext": "10.0.0.0", "System.Buffers": "10.0.0.0", "System.Collections.Concurrent": "10.0.0.0", "System.Collections": "10.0.0.0", "System.Collections.Immutable": "10.0.0.0", "System.Collections.NonGeneric": "10.0.0.0", "System.Collections.Specialized": "10.0.0.0", "System.ComponentModel.Annotations": "10.0.0.0", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel": "10.0.0.0", "System.ComponentModel.EventBasedAsync": "10.0.0.0", "System.ComponentModel.Primitives": "10.0.0.0", "System.ComponentModel.TypeConverter": "10.0.0.0", "System.Configuration": "*******", "System.Console": "10.0.0.0", "System.Core": "*******", "System.Data.Common": "10.0.0.0", "System.Data.DataSetExtensions": "10.0.0.0", "System.Data": "*******", "System.Diagnostics.Contracts": "10.0.0.0", "System.Diagnostics.Debug": "10.0.0.0", "System.Diagnostics.DiagnosticSource": "10.0.0.0", "System.Diagnostics.EventLog.Reference": "10.0.0.0", "System.Diagnostics.FileVersionInfo": "10.0.0.0", "System.Diagnostics.Process": "10.0.0.0", "System.Diagnostics.StackTrace": "10.0.0.0", "System.Diagnostics.TextWriterTraceListener": "10.0.0.0", "System.Diagnostics.Tools": "10.0.0.0", "System.Diagnostics.TraceSource": "10.0.0.0", "System.Diagnostics.Tracing": "10.0.0.0", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "10.0.0.0", "System.Dynamic.Runtime": "10.0.0.0", "System.Formats.Asn1": "10.0.0.0", "System.Formats.Cbor": "10.0.0.0", "System.Formats.Tar": "10.0.0.0", "System.Globalization.Calendars": "10.0.0.0", "System.Globalization": "10.0.0.0", "System.Globalization.Extensions": "10.0.0.0", "System.IO.Compression.Brotli": "10.0.0.0", "System.IO.Compression": "10.0.0.0", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile": "10.0.0.0", "System.IO": "10.0.0.0", "System.IO.FileSystem.AccessControl": "10.0.0.0", "System.IO.FileSystem": "10.0.0.0", "System.IO.FileSystem.DriveInfo": "10.0.0.0", "System.IO.FileSystem.Primitives": "10.0.0.0", "System.IO.FileSystem.Watcher": "10.0.0.0", "System.IO.IsolatedStorage": "10.0.0.0", "System.IO.MemoryMappedFiles": "10.0.0.0", "System.IO.Pipelines": "10.0.0.0", "System.IO.Pipes.AccessControl": "10.0.0.0", "System.IO.Pipes": "10.0.0.0", "System.IO.UnmanagedMemoryStream": "10.0.0.0", "System.Linq.AsyncEnumerable": "10.0.0.0", "System.Linq": "10.0.0.0", "System.Linq.Expressions": "10.0.0.0", "System.Linq.Parallel": "10.0.0.0", "System.Linq.Queryable": "10.0.0.0", "System.Memory": "10.0.0.0", "System.Net": "*******", "System.Net.Http": "10.0.0.0", "System.Net.Http.Json": "10.0.0.0", "System.Net.HttpListener": "10.0.0.0", "System.Net.Mail": "10.0.0.0", "System.Net.NameResolution": "10.0.0.0", "System.Net.NetworkInformation": "10.0.0.0", "System.Net.Ping": "10.0.0.0", "System.Net.Primitives": "10.0.0.0", "System.Net.Quic": "10.0.0.0", "System.Net.Requests": "10.0.0.0", "System.Net.Security": "10.0.0.0", "System.Net.ServerSentEvents": "10.0.0.0", "System.Net.ServicePoint": "10.0.0.0", "System.Net.Sockets": "10.0.0.0", "System.Net.WebClient": "10.0.0.0", "System.Net.WebHeaderCollection": "10.0.0.0", "System.Net.WebProxy": "10.0.0.0", "System.Net.WebSockets.Client": "10.0.0.0", "System.Net.WebSockets": "10.0.0.0", "System.Numerics": "*******", "System.Numerics.Vectors": "10.0.0.0", "System.ObjectModel": "10.0.0.0", "System.Reflection.DispatchProxy": "10.0.0.0", "System.Reflection": "10.0.0.0", "System.Reflection.Emit": "10.0.0.0", "System.Reflection.Emit.ILGeneration": "10.0.0.0", "System.Reflection.Emit.Lightweight": "10.0.0.0", "System.Reflection.Extensions": "10.0.0.0", "System.Reflection.Metadata": "10.0.0.0", "System.Reflection.Primitives": "10.0.0.0", "System.Reflection.TypeExtensions": "10.0.0.0", "System.Resources.Reader": "10.0.0.0", "System.Resources.ResourceManager": "10.0.0.0", "System.Resources.Writer": "10.0.0.0", "System.Runtime.CompilerServices.Unsafe": "10.0.0.0", "System.Runtime.CompilerServices.VisualC": "10.0.0.0", "System.Runtime": "10.0.0.0", "System.Runtime.Extensions": "10.0.0.0", "System.Runtime.Handles": "10.0.0.0", "System.Runtime.InteropServices": "10.0.0.0", "System.Runtime.InteropServices.JavaScript": "10.0.0.0", "System.Runtime.InteropServices.RuntimeInformation": "10.0.0.0", "System.Runtime.Intrinsics": "10.0.0.0", "System.Runtime.Loader": "10.0.0.0", "System.Runtime.Numerics": "10.0.0.0", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "10.0.0.0", "System.Runtime.Serialization.Primitives": "10.0.0.0", "System.Runtime.Serialization.Xml": "10.0.0.0", "System.Security.AccessControl": "10.0.0.0", "System.Security.Claims": "10.0.0.0", "System.Security.Cryptography.Algorithms": "10.0.0.0", "System.Security.Cryptography.Cng": "10.0.0.0", "System.Security.Cryptography.Csp": "10.0.0.0", "System.Security.Cryptography": "10.0.0.0", "System.Security.Cryptography.Encoding": "10.0.0.0", "System.Security.Cryptography.OpenSsl": "10.0.0.0", "System.Security.Cryptography.Primitives": "10.0.0.0", "System.Security.Cryptography.X509Certificates": "10.0.0.0", "System.Security.Cryptography.Xml": "10.0.0.0", "System.Security": "*******", "System.Security.Principal": "10.0.0.0", "System.Security.Principal.Windows": "10.0.0.0", "System.Security.SecureString": "10.0.0.0", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages": "10.0.0.0", "System.Text.Encoding": "10.0.0.0", "System.Text.Encoding.Extensions": "10.0.0.0", "System.Text.Encodings.Web": "10.0.0.0", "System.Text.Json": "10.0.0.0", "System.Text.RegularExpressions": "10.0.0.0", "System.Threading.AccessControl": "10.0.0.0", "System.Threading.Channels": "10.0.0.0", "System.Threading": "10.0.0.0", "System.Threading.Overlapped": "10.0.0.0", "System.Threading.RateLimiting": "10.0.0.0", "System.Threading.Tasks.Dataflow": "10.0.0.0", "System.Threading.Tasks": "10.0.0.0", "System.Threading.Tasks.Extensions": "10.0.0.0", "System.Threading.Tasks.Parallel": "10.0.0.0", "System.Threading.Thread": "10.0.0.0", "System.Threading.ThreadPool": "10.0.0.0", "System.Threading.Timer": "10.0.0.0", "System.Transactions": "*******", "System.Transactions.Local": "10.0.0.0", "System.ValueTuple": "10.0.0.0", "System.Web": "*******", "System.Web.HttpUtility": "10.0.0.0", "System.Windows": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter": "10.0.0.0", "System.Xml.Serialization": "*******", "System.Xml.XDocument": "10.0.0.0", "System.Xml.XmlDocument": "10.0.0.0", "System.Xml.XmlSerializer": "10.0.0.0", "System.Xml.XPath": "10.0.0.0", "System.Xml.XPath.XDocument": "10.0.0.0", "WindowsBase": "*******"}, "runtime": {"auth-service.Tests.dll": {}}, "compile": {"auth-service.Tests.dll": {}}}, "Castle.Core/5.1.1": {"dependencies": {"System.Diagnostics.EventLog": "9.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.1.0"}}, "compile": {"lib/net6.0/Castle.Core.dll": {}}}, "FluentAssertions/6.12.2": {"dependencies": {"System.Configuration.ConfigurationManager": "4.4.0"}, "runtime": {"lib/net6.0/FluentAssertions.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/net6.0/FluentAssertions.dll": {}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.11": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "********", "fileVersion": "8.0.1124.52116"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {}}}, "Microsoft.AspNetCore.Mvc.Testing/9.0.0": {"dependencies": {"Microsoft.AspNetCore.TestHost": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Microsoft.Extensions.Hosting": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Mvc.Testing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Mvc.Testing.dll": {}}}, "Microsoft.AspNetCore.OpenApi/8.0.0": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}, "compile": {"lib/net8.0/Microsoft.AspNetCore.OpenApi.dll": {}}}, "Microsoft.AspNetCore.TestHost/9.0.0": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.TestHost.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.TestHost.dll": {}}}, "Microsoft.CodeCoverage/17.12.0": {"runtime": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.524.48002"}}, "compile": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.IdentityModel.Abstractions/8.12.1": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "8.12.1.60617"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {}}}, "Microsoft.IdentityModel.JsonWebTokens/8.12.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.12.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "********", "fileVersion": "8.12.1.60617"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {}}}, "Microsoft.IdentityModel.Logging/8.12.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.12.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "********", "fileVersion": "8.12.1.60617"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {}}}, "Microsoft.IdentityModel.Protocols/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.12.1", "Microsoft.IdentityModel.Tokens": "8.12.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.1.2", "System.IdentityModel.Tokens.Jwt": "8.12.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "7.1.2.0", "fileVersion": "7.1.2.41121"}}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {}}}, "Microsoft.IdentityModel.Tokens/8.12.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.IdentityModel.Logging": "8.12.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "********", "fileVersion": "8.12.1.60617"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {}}}, "Microsoft.NET.Test.Sdk/17.12.0": {"dependencies": {"Microsoft.CodeCoverage": "17.12.0", "Microsoft.TestPlatform.TestHost": "17.12.0"}}, "Microsoft.OpenApi/1.4.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}}, "Microsoft.TestPlatform.ObjectModel/17.12.0": {"runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}}, "Microsoft.TestPlatform.TestHost/17.12.0": {"dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.12.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/testhost.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}, "lib/netcoreapp3.1/testhost.dll": {}}}, "MimeMapping/3.0.1": {"runtime": {"lib/netstandard2.0/MimeMapping.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/MimeMapping.dll": {}}}, "Moq/4.20.72": {"dependencies": {"Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Moq.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "compile": {"lib/net6.0/Moq.dll": {}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}, "compile": {"lib/net6.0/Newtonsoft.Json.dll": {}}}, "Supabase/1.1.1": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Supabase.Core": "1.0.0", "Supabase.Functions": "2.0.0", "Supabase.Gotrue": "6.0.3", "Supabase.Postgrest": "4.0.3", "Supabase.Realtime": "7.0.2", "Supabase.Storage": "2.0.2"}, "runtime": {"lib/netstandard2.1/Supabase.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.1/Supabase.dll": {}}}, "Supabase.Core/1.0.0": {"runtime": {"lib/netstandard2.0/Supabase.Core.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}, "compile": {"lib/netstandard2.0/Supabase.Core.dll": {}}}, "Supabase.Functions/2.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Supabase.Core": "1.0.0"}, "runtime": {"lib/netstandard2.0/Supabase.Functions.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}, "compile": {"lib/netstandard2.0/Supabase.Functions.dll": {}}}, "Supabase.Gotrue/6.0.3": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Supabase.Core": "1.0.0", "System.IdentityModel.Tokens.Jwt": "8.12.1"}, "runtime": {"lib/netstandard2.1/Supabase.Gotrue.dll": {"assemblyVersion": "6.0.3.0", "fileVersion": "6.0.3.0"}}, "compile": {"lib/netstandard2.1/Supabase.Gotrue.dll": {}}}, "Supabase.Postgrest/4.0.3": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Supabase.Core": "1.0.0"}, "runtime": {"lib/netstandard2.0/Supabase.Postgrest.dll": {"assemblyVersion": "4.0.3.0", "fileVersion": "4.0.3.0"}}, "compile": {"lib/netstandard2.0/Supabase.Postgrest.dll": {}}}, "Supabase.Realtime/7.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Supabase.Core": "1.0.0", "Supabase.Postgrest": "4.0.3", "Websocket.Client": "5.1.1"}, "runtime": {"lib/netstandard2.1/Supabase.Realtime.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.1/Supabase.Realtime.dll": {}}}, "Supabase.Storage/2.0.2": {"dependencies": {"MimeMapping": "3.0.1", "Newtonsoft.Json": "13.0.3", "Supabase.Core": "1.0.0"}, "runtime": {"lib/netstandard2.0/Supabase.Storage.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Supabase.Storage.dll": {}}}, "Swashbuckle.AspNetCore/6.5.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.5.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.5.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.5.0"}}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.5.0"}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.Configuration.ConfigurationManager/4.4.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.4.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}, "compile": {"ref/netstandard2.0/System.Configuration.ConfigurationManager.dll": {}}}, "System.Diagnostics.EventLog/9.0.0": {"runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.IdentityModel.Tokens.Jwt/8.12.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.12.1", "Microsoft.IdentityModel.Tokens": "8.12.1"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "********", "fileVersion": "8.12.1.60617"}}, "compile": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {}}}, "System.Reactive/6.0.0": {"runtime": {"lib/net6.0/System.Reactive.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.1"}}, "compile": {"lib/net6.0/System.Reactive.dll": {}}}, "System.Security.Cryptography.ProtectedData/4.4.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.25519.3"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.2.0", "fileVersion": "4.6.25519.3"}}}, "Websocket.Client/5.1.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "System.Reactive": "6.0.0"}, "runtime": {"lib/net8.0/Websocket.Client.dll": {"assemblyVersion": "5.1.1.0", "fileVersion": "5.1.1.0"}}, "compile": {"lib/net8.0/Websocket.Client.dll": {}}}, "xunit/2.9.2": {"dependencies": {"xunit.analyzers": "1.16.0", "xunit.assert": "2.9.2", "xunit.core": "2.9.2"}}, "xunit.abstractions/2.0.3": {"runtime": {"lib/netstandard2.0/xunit.abstractions.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "0.0.0.0"}}, "compile": {"lib/netstandard2.0/xunit.abstractions.dll": {}}}, "xunit.assert/2.9.2": {"runtime": {"lib/net6.0/xunit.assert.dll": {"assemblyVersion": "2.9.2.0", "fileVersion": "2.9.2.0"}}, "compile": {"lib/net6.0/xunit.assert.dll": {}}}, "xunit.core/2.9.2": {"dependencies": {"xunit.extensibility.core": "2.9.2", "xunit.extensibility.execution": "2.9.2"}}, "xunit.extensibility.core/2.9.2": {"dependencies": {"xunit.abstractions": "2.0.3"}, "runtime": {"lib/netstandard1.1/xunit.core.dll": {"assemblyVersion": "2.9.2.0", "fileVersion": "2.9.2.0"}}, "compile": {"lib/netstandard1.1/xunit.core.dll": {}}}, "xunit.extensibility.execution/2.9.2": {"dependencies": {"xunit.extensibility.core": "2.9.2"}, "runtime": {"lib/netstandard1.1/xunit.execution.dotnet.dll": {"assemblyVersion": "2.9.2.0", "fileVersion": "2.9.2.0"}}, "compile": {"lib/netstandard1.1/xunit.execution.dotnet.dll": {}}}, "AuthService/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.11", "Microsoft.AspNetCore.OpenApi": "8.0.0", "Microsoft.IdentityModel.Tokens": "8.12.1", "Newtonsoft.Json": "13.0.3", "Supabase": "1.1.1", "Swashbuckle.AspNetCore": "6.5.0", "System.IdentityModel.Tokens.Jwt": "8.12.1"}, "runtime": {"AuthService.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}, "compile": {"AuthService.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Antiforgery.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.BearerToken/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authentication.BearerToken.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Cookies/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authentication.Cookies.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Core/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authentication.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authentication.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OAuth/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authentication.OAuth.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Policy/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authorization.Policy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Authorization/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Components.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Components.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Endpoints/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Components.Endpoints.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Forms/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Components.Forms.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Server/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Components.Server.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Web/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Components.Web.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Connections.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Connections.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.CookiePolicy/10.0.0.0": {"compile": {"Microsoft.AspNetCore.CookiePolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cors/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.Internal/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Cryptography.Internal.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection/10.0.0.0": {"compile": {"Microsoft.AspNetCore.DataProtection.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Extensions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore/10.0.0.0": {"compile": {"Microsoft.AspNetCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HostFiltering/10.0.0.0": {"compile": {"Microsoft.AspNetCore.HostFiltering.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Html.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections.Common/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.Connections.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.Connections.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Extensions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Features/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.Features.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Results/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.Results.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpLogging/10.0.0.0": {"compile": {"Microsoft.AspNetCore.HttpLogging.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpOverrides/10.0.0.0": {"compile": {"Microsoft.AspNetCore.HttpOverrides.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpsPolicy/10.0.0.0": {"compile": {"Microsoft.AspNetCore.HttpsPolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Identity.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization.Routing/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Localization.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Metadata/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Metadata.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ApiExplorer/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Core/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Cors/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.DataAnnotations/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Json/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Localization/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.RazorPages/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.TagHelpers/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ViewFeatures/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.OutputCaching/10.0.0.0": {"compile": {"Microsoft.AspNetCore.OutputCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RateLimiting/10.0.0.0": {"compile": {"Microsoft.AspNetCore.RateLimiting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Runtime/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RequestDecompression/10.0.0.0": {"compile": {"Microsoft.AspNetCore.RequestDecompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching/10.0.0.0": {"compile": {"Microsoft.AspNetCore.ResponseCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCompression/10.0.0.0": {"compile": {"Microsoft.AspNetCore.ResponseCompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Rewrite/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Rewrite.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.HttpSys/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.HttpSys.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IIS/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.IIS.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IISIntegration/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Core/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Session/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Session.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Common/10.0.0.0": {"compile": {"Microsoft.AspNetCore.SignalR.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Core/10.0.0.0": {"compile": {"Microsoft.AspNetCore.SignalR.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR/10.0.0.0": {"compile": {"Microsoft.AspNetCore.SignalR.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Protocols.Json/10.0.0.0": {"compile": {"Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticAssets/10.0.0.0": {"compile": {"Microsoft.AspNetCore.StaticAssets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticFiles/10.0.0.0": {"compile": {"Microsoft.AspNetCore.StaticFiles.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebSockets/10.0.0.0": {"compile": {"Microsoft.AspNetCore.WebSockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebUtilities/10.0.0.0": {"compile": {"Microsoft.AspNetCore.WebUtilities.dll": {}}, "compileOnly": true}, "Microsoft.CSharp/10.0.0.0": {"compile": {"Microsoft.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Abstractions/10.0.0.0": {"compile": {"Microsoft.Extensions.Caching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Memory/10.0.0.0": {"compile": {"Microsoft.Extensions.Caching.Memory.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Abstractions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Binder.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.Binder.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.CommandLine.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.FileExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Ini/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.Ini.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Json.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.Json.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.KeyPerFile/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.UserSecrets.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Xml/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.DependencyInjection.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/10.0.0.0": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks/10.0.0.0": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Features/10.0.0.0": {"compile": {"Microsoft.Extensions.Features.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.FileProviders.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Composite/10.0.0.0": {"compile": {"Microsoft.Extensions.FileProviders.Composite.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Embedded/10.0.0.0": {"compile": {"Microsoft.Extensions.FileProviders.Embedded.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Physical.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.FileProviders.Physical.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileSystemGlobbing.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.FileSystemGlobbing.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Abstractions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Http.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Http.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Core/10.0.0.0": {"compile": {"Microsoft.Extensions.Identity.Core.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Stores/10.0.0.0": {"compile": {"Microsoft.Extensions.Identity.Stores.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization.Abstractions/10.0.0.0": {"compile": {"Microsoft.Extensions.Localization.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization/10.0.0.0": {"compile": {"Microsoft.Extensions.Localization.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Abstractions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Configuration.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.Console.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.Debug.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventLog.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.EventLog.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.EventSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.TraceSource/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.TraceSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.ObjectPool/10.0.0.0": {"compile": {"Microsoft.Extensions.ObjectPool.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.DataAnnotations/10.0.0.0": {"compile": {"Microsoft.Extensions.Options.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Options.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Primitives.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Validation/10.0.0.0": {"compile": {"Microsoft.Extensions.Validation.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.WebEncoders/10.0.0.0": {"compile": {"Microsoft.Extensions.WebEncoders.dll": {}}, "compileOnly": true}, "Microsoft.JSInterop/10.0.0.0": {"compile": {"Microsoft.JSInterop.dll": {}}, "compileOnly": true}, "Microsoft.Net.Http.Headers/10.0.0.0": {"compile": {"Microsoft.Net.Http.Headers.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic.Core/********": {"compile": {"Microsoft.VisualBasic.Core.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic/10.0.0.0": {"compile": {"Microsoft.VisualBasic.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Primitives/10.0.0.0": {"compile": {"Microsoft.Win32.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Registry/10.0.0.0": {"compile": {"Microsoft.Win32.Registry.dll": {}}, "compileOnly": true}, "mscorlib/*******": {"compile": {"mscorlib.dll": {}}, "compileOnly": true}, "netstandard/2.1.0.0": {"compile": {"netstandard.dll": {}}, "compileOnly": true}, "System.AppContext/10.0.0.0": {"compile": {"System.AppContext.dll": {}}, "compileOnly": true}, "System.Buffers/10.0.0.0": {"compile": {"System.Buffers.dll": {}}, "compileOnly": true}, "System.Collections.Concurrent/10.0.0.0": {"compile": {"System.Collections.Concurrent.dll": {}}, "compileOnly": true}, "System.Collections/10.0.0.0": {"compile": {"System.Collections.dll": {}}, "compileOnly": true}, "System.Collections.Immutable/10.0.0.0": {"compile": {"System.Collections.Immutable.dll": {}}, "compileOnly": true}, "System.Collections.NonGeneric/10.0.0.0": {"compile": {"System.Collections.NonGeneric.dll": {}}, "compileOnly": true}, "System.Collections.Specialized/10.0.0.0": {"compile": {"System.Collections.Specialized.dll": {}}, "compileOnly": true}, "System.ComponentModel.Annotations/10.0.0.0": {"compile": {"System.ComponentModel.Annotations.dll": {}}, "compileOnly": true}, "System.ComponentModel.DataAnnotations/*******": {"compile": {"System.ComponentModel.DataAnnotations.dll": {}}, "compileOnly": true}, "System.ComponentModel/10.0.0.0": {"compile": {"System.ComponentModel.dll": {}}, "compileOnly": true}, "System.ComponentModel.EventBasedAsync/10.0.0.0": {"compile": {"System.ComponentModel.EventBasedAsync.dll": {}}, "compileOnly": true}, "System.ComponentModel.Primitives/10.0.0.0": {"compile": {"System.ComponentModel.Primitives.dll": {}}, "compileOnly": true}, "System.ComponentModel.TypeConverter/10.0.0.0": {"compile": {"System.ComponentModel.TypeConverter.dll": {}}, "compileOnly": true}, "System.Configuration/*******": {"compile": {"System.Configuration.dll": {}}, "compileOnly": true}, "System.Console/10.0.0.0": {"compile": {"System.Console.dll": {}}, "compileOnly": true}, "System.Core/*******": {"compile": {"System.Core.dll": {}}, "compileOnly": true}, "System.Data.Common/10.0.0.0": {"compile": {"System.Data.Common.dll": {}}, "compileOnly": true}, "System.Data.DataSetExtensions/10.0.0.0": {"compile": {"System.Data.DataSetExtensions.dll": {}}, "compileOnly": true}, "System.Data/*******": {"compile": {"System.Data.dll": {}}, "compileOnly": true}, "System.Diagnostics.Contracts/10.0.0.0": {"compile": {"System.Diagnostics.Contracts.dll": {}}, "compileOnly": true}, "System.Diagnostics.Debug/10.0.0.0": {"compile": {"System.Diagnostics.Debug.dll": {}}, "compileOnly": true}, "System.Diagnostics.DiagnosticSource/10.0.0.0": {"compile": {"System.Diagnostics.DiagnosticSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.EventLog.Reference/10.0.0.0": {"compile": {"System.Diagnostics.EventLog.dll": {}}, "compileOnly": true}, "System.Diagnostics.FileVersionInfo/10.0.0.0": {"compile": {"System.Diagnostics.FileVersionInfo.dll": {}}, "compileOnly": true}, "System.Diagnostics.Process/10.0.0.0": {"compile": {"System.Diagnostics.Process.dll": {}}, "compileOnly": true}, "System.Diagnostics.StackTrace/10.0.0.0": {"compile": {"System.Diagnostics.StackTrace.dll": {}}, "compileOnly": true}, "System.Diagnostics.TextWriterTraceListener/10.0.0.0": {"compile": {"System.Diagnostics.TextWriterTraceListener.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tools/10.0.0.0": {"compile": {"System.Diagnostics.Tools.dll": {}}, "compileOnly": true}, "System.Diagnostics.TraceSource/10.0.0.0": {"compile": {"System.Diagnostics.TraceSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tracing/10.0.0.0": {"compile": {"System.Diagnostics.Tracing.dll": {}}, "compileOnly": true}, "System/*******": {"compile": {"System.dll": {}}, "compileOnly": true}, "System.Drawing/*******": {"compile": {"System.Drawing.dll": {}}, "compileOnly": true}, "System.Drawing.Primitives/10.0.0.0": {"compile": {"System.Drawing.Primitives.dll": {}}, "compileOnly": true}, "System.Dynamic.Runtime/10.0.0.0": {"compile": {"System.Dynamic.Runtime.dll": {}}, "compileOnly": true}, "System.Formats.Asn1/10.0.0.0": {"compile": {"System.Formats.Asn1.dll": {}}, "compileOnly": true}, "System.Formats.Cbor/10.0.0.0": {"compile": {"System.Formats.Cbor.dll": {}}, "compileOnly": true}, "System.Formats.Tar/10.0.0.0": {"compile": {"System.Formats.Tar.dll": {}}, "compileOnly": true}, "System.Globalization.Calendars/10.0.0.0": {"compile": {"System.Globalization.Calendars.dll": {}}, "compileOnly": true}, "System.Globalization/10.0.0.0": {"compile": {"System.Globalization.dll": {}}, "compileOnly": true}, "System.Globalization.Extensions/10.0.0.0": {"compile": {"System.Globalization.Extensions.dll": {}}, "compileOnly": true}, "System.IO.Compression.Brotli/10.0.0.0": {"compile": {"System.IO.Compression.Brotli.dll": {}}, "compileOnly": true}, "System.IO.Compression/10.0.0.0": {"compile": {"System.IO.Compression.dll": {}}, "compileOnly": true}, "System.IO.Compression.FileSystem/*******": {"compile": {"System.IO.Compression.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.Compression.ZipFile/10.0.0.0": {"compile": {"System.IO.Compression.ZipFile.dll": {}}, "compileOnly": true}, "System.IO/10.0.0.0": {"compile": {"System.IO.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.AccessControl/10.0.0.0": {"compile": {"System.IO.FileSystem.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.FileSystem/10.0.0.0": {"compile": {"System.IO.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.DriveInfo/10.0.0.0": {"compile": {"System.IO.FileSystem.DriveInfo.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Primitives/10.0.0.0": {"compile": {"System.IO.FileSystem.Primitives.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Watcher/10.0.0.0": {"compile": {"System.IO.FileSystem.Watcher.dll": {}}, "compileOnly": true}, "System.IO.IsolatedStorage/10.0.0.0": {"compile": {"System.IO.IsolatedStorage.dll": {}}, "compileOnly": true}, "System.IO.MemoryMappedFiles/10.0.0.0": {"compile": {"System.IO.MemoryMappedFiles.dll": {}}, "compileOnly": true}, "System.IO.Pipelines/10.0.0.0": {"compile": {"System.IO.Pipelines.dll": {}}, "compileOnly": true}, "System.IO.Pipes.AccessControl/10.0.0.0": {"compile": {"System.IO.Pipes.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.Pipes/10.0.0.0": {"compile": {"System.IO.Pipes.dll": {}}, "compileOnly": true}, "System.IO.UnmanagedMemoryStream/10.0.0.0": {"compile": {"System.IO.UnmanagedMemoryStream.dll": {}}, "compileOnly": true}, "System.Linq.AsyncEnumerable/10.0.0.0": {"compile": {"System.Linq.AsyncEnumerable.dll": {}}, "compileOnly": true}, "System.Linq/10.0.0.0": {"compile": {"System.Linq.dll": {}}, "compileOnly": true}, "System.Linq.Expressions/10.0.0.0": {"compile": {"System.Linq.Expressions.dll": {}}, "compileOnly": true}, "System.Linq.Parallel/10.0.0.0": {"compile": {"System.Linq.Parallel.dll": {}}, "compileOnly": true}, "System.Linq.Queryable/10.0.0.0": {"compile": {"System.Linq.Queryable.dll": {}}, "compileOnly": true}, "System.Memory/10.0.0.0": {"compile": {"System.Memory.dll": {}}, "compileOnly": true}, "System.Net/*******": {"compile": {"System.Net.dll": {}}, "compileOnly": true}, "System.Net.Http/10.0.0.0": {"compile": {"System.Net.Http.dll": {}}, "compileOnly": true}, "System.Net.Http.Json/10.0.0.0": {"compile": {"System.Net.Http.Json.dll": {}}, "compileOnly": true}, "System.Net.HttpListener/10.0.0.0": {"compile": {"System.Net.HttpListener.dll": {}}, "compileOnly": true}, "System.Net.Mail/10.0.0.0": {"compile": {"System.Net.Mail.dll": {}}, "compileOnly": true}, "System.Net.NameResolution/10.0.0.0": {"compile": {"System.Net.NameResolution.dll": {}}, "compileOnly": true}, "System.Net.NetworkInformation/10.0.0.0": {"compile": {"System.Net.NetworkInformation.dll": {}}, "compileOnly": true}, "System.Net.Ping/10.0.0.0": {"compile": {"System.Net.Ping.dll": {}}, "compileOnly": true}, "System.Net.Primitives/10.0.0.0": {"compile": {"System.Net.Primitives.dll": {}}, "compileOnly": true}, "System.Net.Quic/10.0.0.0": {"compile": {"System.Net.Quic.dll": {}}, "compileOnly": true}, "System.Net.Requests/10.0.0.0": {"compile": {"System.Net.Requests.dll": {}}, "compileOnly": true}, "System.Net.Security/10.0.0.0": {"compile": {"System.Net.Security.dll": {}}, "compileOnly": true}, "System.Net.ServerSentEvents/10.0.0.0": {"compile": {"System.Net.ServerSentEvents.dll": {}}, "compileOnly": true}, "System.Net.ServicePoint/10.0.0.0": {"compile": {"System.Net.ServicePoint.dll": {}}, "compileOnly": true}, "System.Net.Sockets/10.0.0.0": {"compile": {"System.Net.Sockets.dll": {}}, "compileOnly": true}, "System.Net.WebClient/10.0.0.0": {"compile": {"System.Net.WebClient.dll": {}}, "compileOnly": true}, "System.Net.WebHeaderCollection/10.0.0.0": {"compile": {"System.Net.WebHeaderCollection.dll": {}}, "compileOnly": true}, "System.Net.WebProxy/10.0.0.0": {"compile": {"System.Net.WebProxy.dll": {}}, "compileOnly": true}, "System.Net.WebSockets.Client/10.0.0.0": {"compile": {"System.Net.WebSockets.Client.dll": {}}, "compileOnly": true}, "System.Net.WebSockets/10.0.0.0": {"compile": {"System.Net.WebSockets.dll": {}}, "compileOnly": true}, "System.Numerics/*******": {"compile": {"System.Numerics.dll": {}}, "compileOnly": true}, "System.Numerics.Vectors/10.0.0.0": {"compile": {"System.Numerics.Vectors.dll": {}}, "compileOnly": true}, "System.ObjectModel/10.0.0.0": {"compile": {"System.ObjectModel.dll": {}}, "compileOnly": true}, "System.Reflection.DispatchProxy/10.0.0.0": {"compile": {"System.Reflection.DispatchProxy.dll": {}}, "compileOnly": true}, "System.Reflection/10.0.0.0": {"compile": {"System.Reflection.dll": {}}, "compileOnly": true}, "System.Reflection.Emit/10.0.0.0": {"compile": {"System.Reflection.Emit.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.ILGeneration/10.0.0.0": {"compile": {"System.Reflection.Emit.ILGeneration.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Lightweight/10.0.0.0": {"compile": {"System.Reflection.Emit.Lightweight.dll": {}}, "compileOnly": true}, "System.Reflection.Extensions/10.0.0.0": {"compile": {"System.Reflection.Extensions.dll": {}}, "compileOnly": true}, "System.Reflection.Metadata/10.0.0.0": {"compile": {"System.Reflection.Metadata.dll": {}}, "compileOnly": true}, "System.Reflection.Primitives/10.0.0.0": {"compile": {"System.Reflection.Primitives.dll": {}}, "compileOnly": true}, "System.Reflection.TypeExtensions/10.0.0.0": {"compile": {"System.Reflection.TypeExtensions.dll": {}}, "compileOnly": true}, "System.Resources.Reader/10.0.0.0": {"compile": {"System.Resources.Reader.dll": {}}, "compileOnly": true}, "System.Resources.ResourceManager/10.0.0.0": {"compile": {"System.Resources.ResourceManager.dll": {}}, "compileOnly": true}, "System.Resources.Writer/10.0.0.0": {"compile": {"System.Resources.Writer.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.Unsafe/10.0.0.0": {"compile": {"System.Runtime.CompilerServices.Unsafe.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.VisualC/10.0.0.0": {"compile": {"System.Runtime.CompilerServices.VisualC.dll": {}}, "compileOnly": true}, "System.Runtime/10.0.0.0": {"compile": {"System.Runtime.dll": {}}, "compileOnly": true}, "System.Runtime.Extensions/10.0.0.0": {"compile": {"System.Runtime.Extensions.dll": {}}, "compileOnly": true}, "System.Runtime.Handles/10.0.0.0": {"compile": {"System.Runtime.Handles.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices/10.0.0.0": {"compile": {"System.Runtime.InteropServices.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.JavaScript/10.0.0.0": {"compile": {"System.Runtime.InteropServices.JavaScript.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.RuntimeInformation/10.0.0.0": {"compile": {"System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "compileOnly": true}, "System.Runtime.Intrinsics/10.0.0.0": {"compile": {"System.Runtime.Intrinsics.dll": {}}, "compileOnly": true}, "System.Runtime.Loader/10.0.0.0": {"compile": {"System.Runtime.Loader.dll": {}}, "compileOnly": true}, "System.Runtime.Numerics/10.0.0.0": {"compile": {"System.Runtime.Numerics.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization/*******": {"compile": {"System.Runtime.Serialization.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Formatters/*******": {"compile": {"System.Runtime.Serialization.Formatters.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Json/10.0.0.0": {"compile": {"System.Runtime.Serialization.Json.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Primitives/10.0.0.0": {"compile": {"System.Runtime.Serialization.Primitives.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Xml/10.0.0.0": {"compile": {"System.Runtime.Serialization.Xml.dll": {}}, "compileOnly": true}, "System.Security.AccessControl/10.0.0.0": {"compile": {"System.Security.AccessControl.dll": {}}, "compileOnly": true}, "System.Security.Claims/10.0.0.0": {"compile": {"System.Security.Claims.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Algorithms/10.0.0.0": {"compile": {"System.Security.Cryptography.Algorithms.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Cng/10.0.0.0": {"compile": {"System.Security.Cryptography.Cng.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Csp/10.0.0.0": {"compile": {"System.Security.Cryptography.Csp.dll": {}}, "compileOnly": true}, "System.Security.Cryptography/10.0.0.0": {"compile": {"System.Security.Cryptography.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Encoding/10.0.0.0": {"compile": {"System.Security.Cryptography.Encoding.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.OpenSsl/10.0.0.0": {"compile": {"System.Security.Cryptography.OpenSsl.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Primitives/10.0.0.0": {"compile": {"System.Security.Cryptography.Primitives.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.X509Certificates/10.0.0.0": {"compile": {"System.Security.Cryptography.X509Certificates.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Xml/10.0.0.0": {"compile": {"System.Security.Cryptography.Xml.dll": {}}, "compileOnly": true}, "System.Security/*******": {"compile": {"System.Security.dll": {}}, "compileOnly": true}, "System.Security.Principal/10.0.0.0": {"compile": {"System.Security.Principal.dll": {}}, "compileOnly": true}, "System.Security.Principal.Windows/10.0.0.0": {"compile": {"System.Security.Principal.Windows.dll": {}}, "compileOnly": true}, "System.Security.SecureString/10.0.0.0": {"compile": {"System.Security.SecureString.dll": {}}, "compileOnly": true}, "System.ServiceModel.Web/*******": {"compile": {"System.ServiceModel.Web.dll": {}}, "compileOnly": true}, "System.ServiceProcess/*******": {"compile": {"System.ServiceProcess.dll": {}}, "compileOnly": true}, "System.Text.Encoding.CodePages/10.0.0.0": {"compile": {"System.Text.Encoding.CodePages.dll": {}}, "compileOnly": true}, "System.Text.Encoding/10.0.0.0": {"compile": {"System.Text.Encoding.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Extensions/10.0.0.0": {"compile": {"System.Text.Encoding.Extensions.dll": {}}, "compileOnly": true}, "System.Text.Encodings.Web/10.0.0.0": {"compile": {"System.Text.Encodings.Web.dll": {}}, "compileOnly": true}, "System.Text.Json/10.0.0.0": {"compile": {"System.Text.Json.dll": {}}, "compileOnly": true}, "System.Text.RegularExpressions/10.0.0.0": {"compile": {"System.Text.RegularExpressions.dll": {}}, "compileOnly": true}, "System.Threading.AccessControl/10.0.0.0": {"compile": {"System.Threading.AccessControl.dll": {}}, "compileOnly": true}, "System.Threading.Channels/10.0.0.0": {"compile": {"System.Threading.Channels.dll": {}}, "compileOnly": true}, "System.Threading/10.0.0.0": {"compile": {"System.Threading.dll": {}}, "compileOnly": true}, "System.Threading.Overlapped/10.0.0.0": {"compile": {"System.Threading.Overlapped.dll": {}}, "compileOnly": true}, "System.Threading.RateLimiting/10.0.0.0": {"compile": {"System.Threading.RateLimiting.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Dataflow/10.0.0.0": {"compile": {"System.Threading.Tasks.Dataflow.dll": {}}, "compileOnly": true}, "System.Threading.Tasks/10.0.0.0": {"compile": {"System.Threading.Tasks.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Extensions/10.0.0.0": {"compile": {"System.Threading.Tasks.Extensions.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Parallel/10.0.0.0": {"compile": {"System.Threading.Tasks.Parallel.dll": {}}, "compileOnly": true}, "System.Threading.Thread/10.0.0.0": {"compile": {"System.Threading.Thread.dll": {}}, "compileOnly": true}, "System.Threading.ThreadPool/10.0.0.0": {"compile": {"System.Threading.ThreadPool.dll": {}}, "compileOnly": true}, "System.Threading.Timer/10.0.0.0": {"compile": {"System.Threading.Timer.dll": {}}, "compileOnly": true}, "System.Transactions/*******": {"compile": {"System.Transactions.dll": {}}, "compileOnly": true}, "System.Transactions.Local/10.0.0.0": {"compile": {"System.Transactions.Local.dll": {}}, "compileOnly": true}, "System.ValueTuple/10.0.0.0": {"compile": {"System.ValueTuple.dll": {}}, "compileOnly": true}, "System.Web/*******": {"compile": {"System.Web.dll": {}}, "compileOnly": true}, "System.Web.HttpUtility/10.0.0.0": {"compile": {"System.Web.HttpUtility.dll": {}}, "compileOnly": true}, "System.Windows/*******": {"compile": {"System.Windows.dll": {}}, "compileOnly": true}, "System.Xml/*******": {"compile": {"System.Xml.dll": {}}, "compileOnly": true}, "System.Xml.Linq/*******": {"compile": {"System.Xml.Linq.dll": {}}, "compileOnly": true}, "System.Xml.ReaderWriter/10.0.0.0": {"compile": {"System.Xml.ReaderWriter.dll": {}}, "compileOnly": true}, "System.Xml.Serialization/*******": {"compile": {"System.Xml.Serialization.dll": {}}, "compileOnly": true}, "System.Xml.XDocument/10.0.0.0": {"compile": {"System.Xml.XDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlDocument/10.0.0.0": {"compile": {"System.Xml.XmlDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlSerializer/10.0.0.0": {"compile": {"System.Xml.XmlSerializer.dll": {}}, "compileOnly": true}, "System.Xml.XPath/10.0.0.0": {"compile": {"System.Xml.XPath.dll": {}}, "compileOnly": true}, "System.Xml.XPath.XDocument/10.0.0.0": {"compile": {"System.Xml.XPath.XDocument.dll": {}}, "compileOnly": true}, "WindowsBase/*******": {"compile": {"WindowsBase.dll": {}}, "compileOnly": true}, "coverlet.collector/6.0.2": {"compileOnly": true}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"compileOnly": true}, "Microsoft.Extensions.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"compileOnly": true}, "Microsoft.Extensions.Diagnostics/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"compileOnly": true}, "Microsoft.Extensions.Hosting/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Configuration": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Logging.Debug": "9.0.0", "Microsoft.Extensions.Logging.EventLog": "9.0.0", "Microsoft.Extensions.Logging.EventSource": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Http/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Logging.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Configuration": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventLog/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "System.Diagnostics.EventLog": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Options/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Primitives/9.0.0": {"compileOnly": true}, "xunit.analyzers/1.16.0": {"compileOnly": true}, "xunit.runner.visualstudio/2.8.2": {"compileOnly": true}}}, "libraries": {"auth-service.Tests/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Castle.Core/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "path": "castle.core/5.1.1", "hashPath": "castle.core.5.1.1.nupkg.sha512"}, "FluentAssertions/6.12.2": {"type": "package", "serviceable": true, "sha512": "sha512-8YE+xJmT8wgzEpFuzJ4S62oFhEL/AKouMz1RWPEMEUhy9H11aRQlGIWcHurH5BEy7tbF6gb0CJrs0wOw/AtDcQ==", "path": "fluentassertions/6.12.2", "hashPath": "fluentassertions.6.12.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-9KhRuywosM24BPf1R5erwsvIkpRUu1+btVyOPlM3JgrhFVP4pq5Fuzi3vjP01OHXfbCtNhWa+HGkZeqaWdcO5w==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.11", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.11.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Testing/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4tyGN2cb2lVqMwqPhDhXAkTtSci8RJ0cFKVHEU3yj6I4krcbyQE6SJmAQr5Hq8ARyVopKUrQp/qniDje/1I07A==", "path": "microsoft.aspnetcore.mvc.testing/9.0.0", "hashPath": "microsoft.aspnetcore.mvc.testing.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T4mwMvPSOYAp+KeQ4xO8H2rxpiOMJ9W/7yBBkUTMp96AHtGlPN4s7hbax2tM61LxTY775JKL4fiv5grn41EHXw==", "path": "microsoft.aspnetcore.openapi/8.0.0", "hashPath": "microsoft.aspnetcore.openapi.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.TestHost/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T5t8Qac05kJtFzsBxo+B3p0UcLNTRoWQf/1EbpaVBw9d7w2xL6RKYh0mqG+rPn2rulJDKeU3VfAd+r/YHdaKBg==", "path": "microsoft.aspnetcore.testhost/9.0.0", "hashPath": "microsoft.aspnetcore.testhost.9.0.0.nupkg.sha512"}, "Microsoft.CodeCoverage/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-4svMznBd5JM21JIG2xZKGNanAHNXplxf/kQDFfLHXQ3OnpJkayRK/TjacFjA+EYmoyuNXHo/sOETEfcYtAzIrA==", "path": "microsoft.codecoverage/17.12.0", "hashPath": "microsoft.codecoverage.17.12.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "path": "microsoft.extensions.dependencymodel/9.0.0", "hashPath": "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-JzWhET0VOCORyJbqDc1Wtdl8Q/l+I1MjFB0I/Jko+Ma691JZll8X6o9XwZtUce8FkqGuV4uY4/V1808XZOpDVg==", "path": "microsoft.identitymodel.abstractions/8.12.1", "hashPath": "microsoft.identitymodel.abstractions.8.12.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-imi3xiRLzzKxN4m1aR9Z2X8GUmNsVH7GLA6AkwYStNnh3UzupFtHEEVk3GK1fCvnYdRbpnCGNYY6WQb9AfDAKg==", "path": "microsoft.identitymodel.jsonwebtokens/8.12.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.12.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-39HjVkU7Voe2jRLmORRB5PoTmta1ZPKzUZCc6ldlNlLzdx+um0+fAnvfk05LUQPrNxpvb5ZoqF00SrNvyO2Fzg==", "path": "microsoft.identitymodel.logging/8.12.1", "hashPath": "microsoft.identitymodel.logging.8.12.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-SydLwMRFx6EHPWJ+N6+MVaoArN1Htt92b935O3RUWPY1yUF63zEjvd3lBu79eWdZUwedP8TN2I5V9T3nackvIQ==", "path": "microsoft.identitymodel.protocols/7.1.2", "hashPath": "microsoft.identitymodel.protocols.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/7.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-DzgPEABn3eZmIk4lhov0QPcoHkIbnAfgkyDPM7uGuWDHeockR9DdqNCD9Zy30hPfExu5VhbOXn9oPRi+tFUhEQ==", "path": "microsoft.identitymodel.tokens/8.12.1", "hashPath": "microsoft.identitymodel.tokens.8.12.1.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-irv0HuqoH8Ig5i2fO+8dmDNdFdsrO+DoQcedwIlb810qpZHBNQHZLW7C/AHBQDgLLpw2T96vmMAy/aE4Yj55Sg==", "path": "microsoft.io.recyclablememorystream/3.0.0", "hashPath": "microsoft.io.recyclablememorystream.3.0.0.nupkg.sha512"}, "Microsoft.NET.Test.Sdk/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-kt/PKBZ91rFCWxVIJZSgVLk+YR+4KxTuHf799ho8WNiK5ZQpJNAEZCAWX86vcKrs+DiYjiibpYKdGZP6+/N17w==", "path": "microsoft.net.test.sdk/17.12.0", "hashPath": "microsoft.net.test.sdk.17.12.0.nupkg.sha512"}, "Microsoft.OpenApi/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-rURwggB+QZYcSVbDr7HSdhw/FELvMlriW10OeOzjPT7pstefMo7IThhtNtDudxbXhW+lj0NfX72Ka5EDsG8x6w==", "path": "microsoft.openapi/1.4.3", "hashPath": "microsoft.openapi.1.4.3.nupkg.sha512"}, "Microsoft.TestPlatform.ObjectModel/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-TDqkTKLfQuAaPcEb3pDDWnh7b3SyZF+/W9OZvWFp6eJCIiiYFdSB6taE2I6tWrFw5ywhzOb6sreoGJTI6m3rSQ==", "path": "microsoft.testplatform.objectmodel/17.12.0", "hashPath": "microsoft.testplatform.objectmodel.17.12.0.nupkg.sha512"}, "Microsoft.TestPlatform.TestHost/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-MiPEJQNyADfwZ4pJNpQex+t9/jOClBGMiCiVVFuELCMSX2nmNfvUor3uFVxNNCg30uxDP8JDYfPnMXQzsfzYyg==", "path": "microsoft.testplatform.testhost/17.12.0", "hashPath": "microsoft.testplatform.testhost.17.12.0.nupkg.sha512"}, "MimeMapping/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-lhYcUVnWKaqrboAwi05YLCx3wdluM9Sr1Mv5Emhgc8c8yNVvdiSEnQJMdDvgb4grlYTaOmbnhYaezoeateX95w==", "path": "mimemapping/3.0.1", "hashPath": "mimemapping.3.0.1.nupkg.sha512"}, "Moq/4.20.72": {"type": "package", "serviceable": true, "sha512": "sha512-EA55cjyNn8eTNWrgrdZJH5QLFp2L43oxl1tlkoYUKIE9pRwL784OWiTXeCV5ApS+AMYEAlt7Fo03A2XfouvHmQ==", "path": "moq/4.20.72", "hashPath": "moq.4.20.72.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Supabase/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-LW9O05IiZBW3YUDT/gndPKvzT3I7PVgx0j87+AdJu7mT42m7oh2nOnjuDR9pU0E9FE2Ke/QGovrH/u5OjGn7lg==", "path": "supabase/1.1.1", "hashPath": "supabase.1.1.1.nupkg.sha512"}, "Supabase.Core/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fJK3Kfq1alw53AGWHBr+dhPu+BUR5dKuBjGhcxrFRVdsFFFWSD5iPIdTYi0CUQDA2b1OjGudYL1xd51yp4hU9Q==", "path": "supabase.core/1.0.0", "hashPath": "supabase.core.1.0.0.nupkg.sha512"}, "Supabase.Functions/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rc6zlo6XFkQw/B9fEgwpXHtmNPrnnHmtc96PTbviBJ9lzqPT/Un/G3V9MltlORtnG2RLkLvvS/t2AXAO8m2SCQ==", "path": "supabase.functions/2.0.0", "hashPath": "supabase.functions.2.0.0.nupkg.sha512"}, "Supabase.Gotrue/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-8dkg000ib95bJm0ffDPUrALrefBHrkiqcolNjuUEStF2TeytPbV+OtOUJAMBOqZ2f1nsCY61Ck/7NOmOyGteDw==", "path": "supabase.gotrue/6.0.3", "hashPath": "supabase.gotrue.6.0.3.nupkg.sha512"}, "Supabase.Postgrest/4.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-hpDIh+E5bDgZiHMuM6l+RDm9WWlt/T3fXXFQoqzlsrJFesFMNzkK9feVBG5egJyL/OlwmxuLsnPkR7O+bV3Vcw==", "path": "supabase.postgrest/4.0.3", "hashPath": "supabase.postgrest.4.0.3.nupkg.sha512"}, "Supabase.Realtime/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-9nwlR9RR+uyD63VUaiqAPsd76NaeM6ORAHq7otc4G34RzBtf6aqjPg9IF6d1DGIk78/lJP31+D4NJmQhl3PkJQ==", "path": "supabase.realtime/7.0.2", "hashPath": "supabase.realtime.7.0.2.nupkg.sha512"}, "Supabase.Storage/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-YKxgwVgLjxi32ze29FT8aSLifiTVSZSxSyI9taOewp6wuxh+aMIPLKWtGEhFhegLv2iFwGcZ3ybMDkCi6RB7Rw==", "path": "supabase.storage/2.0.2", "hashPath": "supabase.storage.2.0.2.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-FK05XokgjgwlCI6wCT+D4/abtQkL1X1/B9Oas6uIwHFmYrIO9WUD5aLC9IzMs9GnHfUXOtXZ2S43gN1mhs5+aA==", "path": "swashbuckle.aspnetcore/6.5.0", "hashPath": "swashbuckle.aspnetcore.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-XWmCmqyFmoItXKFsQSwQbEAsjDKcxlNf1l+/Ki42hcb6LjKL8m5Db69OTvz5vLonMSRntYO1XLqz0OP+n3vKnA==", "path": "swashbuckle.aspnetcore.swagger/6.5.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/qW8Qdg9OEs7V013tt+94OdPxbRdbhcEbw4NiwGvf4YBcfhL/y7qp/Mjv/cENsQ2L3NqJ2AOu94weBy/h4KvA==", "path": "swashbuckle.aspnetcore.swaggergen/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-OvbvxX+wL8skxTBttcBsVxdh73Fag4xwqEU2edh4JMn7Ws/xJHnY/JB1e9RoCb6XpDxUF3hD9A0Z1lEUx40Pfw==", "path": "swashbuckle.aspnetcore.swaggerui/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-gWwQv/Ug1qWJmHCmN17nAbxJYmQBM/E94QxKLksvUiiKB1Ld3Sc/eK1lgmbSjDFxkQhVuayI/cGFZhpBSodLrg==", "path": "system.configuration.configurationmanager/4.4.0", "hashPath": "system.configuration.configurationmanager.4.4.0.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qd01+AqPhbAG14KtdtIqFk+cxHQFZ/oqRSCoxU1F+Q6Kv0cl726sl7RzU9yLFGd4BUOKdN4XojXF0pQf/R6YeA==", "path": "system.diagnostics.eventlog/9.0.0", "hashPath": "system.diagnostics.eventlog.9.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.12.1": {"type": "package", "serviceable": true, "sha512": "sha512-jlYdVOJrdeyD80ppEqKJ8BhJdrV3MjeA+seURdhC0DnD41GyUA9Ik+P7Sb571ufVVCYIx93GjeqVvY3QyQxZAA==", "path": "system.identitymodel.tokens.jwt/8.12.1", "hashPath": "system.identitymodel.tokens.jwt.8.12.1.nupkg.sha512"}, "System.Reactive/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-31kfaW4ZupZzPsI5PVe77VhnvFF55qgma7KZr/E0iFTs6fmdhhG8j0mgEx620iLTey1EynOkEfnyTjtNEpJzGw==", "path": "system.reactive/6.0.0", "hashPath": "system.reactive.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-cJV7ScGW7EhatRsjehfvvYVBvtiSMKgN8bOVI0bQhnF5bU7vnHVIsH49Kva7i7GWaWYvmEzkYVk1TC+gZYBEog==", "path": "system.security.cryptography.protecteddata/4.4.0", "hashPath": "system.security.cryptography.protecteddata.4.4.0.nupkg.sha512"}, "Websocket.Client/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-VpQV6b8HRnw6bFFIPTOOMtOxba3/viH9K2U2LdOYNjQ2b2HrLHxjodmJr3nPwyNSrtrRPr1RDwOMJ5qqlPnCVg==", "path": "websocket.client/5.1.1", "hashPath": "websocket.client.5.1.1.nupkg.sha512"}, "xunit/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-7LhFS2N9Z6Xgg8aE5lY95cneYivRMfRI8v+4PATa4S64D5Z/Plkg0qa8dTRHSiGRgVZ/CL2gEfJDE5AUhOX+2Q==", "path": "xunit/2.9.2", "hashPath": "xunit.2.9.2.nupkg.sha512"}, "xunit.abstractions/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-pot1I4YOxlWjIb5jmwvvQNbTrZ3lJQ+jUGkGjWE3hEFM0l5gOnBWS+H3qsex68s5cO52g+44vpGzhAt+42vwKg==", "path": "xunit.abstractions/2.0.3", "hashPath": "xunit.abstractions.2.0.3.nupkg.sha512"}, "xunit.assert/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-QkNBAQG4pa66cholm28AxijBjrmki98/vsEh4Sx5iplzotvPgpiotcxqJQMRC8d7RV7nIT8ozh97957hDnZwsQ==", "path": "xunit.assert/2.9.2", "hashPath": "xunit.assert.2.9.2.nupkg.sha512"}, "xunit.core/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-O6RrNSdmZ0xgEn5kT927PNwog5vxTtKrWMihhhrT0Sg9jQ7iBDciYOwzBgP2krBEk5/GBXI18R1lKvmnxGcb4w==", "path": "xunit.core/2.9.2", "hashPath": "xunit.core.2.9.2.nupkg.sha512"}, "xunit.extensibility.core/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-Ol+KlBJz1x8BrdnhN2DeOuLrr1I/cTwtHCggL9BvYqFuVd/TUSzxNT5O0NxCIXth30bsKxgMfdqLTcORtM52yQ==", "path": "xunit.extensibility.core/2.9.2", "hashPath": "xunit.extensibility.core.2.9.2.nupkg.sha512"}, "xunit.extensibility.execution/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-rKMpq4GsIUIJibXuZoZ8lYp5EpROlnYaRpwu9Zr0sRZXE7JqJfEEbCsUriZqB+ByXCLFBJyjkTRULMdC+U566g==", "path": "xunit.extensibility.execution/2.9.2", "hashPath": "xunit.extensibility.execution.2.9.2.nupkg.sha512"}, "AuthService/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Antiforgery/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.BearerToken/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Cookies/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Core/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.OAuth/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Policy/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Authorization/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Endpoints/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Forms/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Server/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Web/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.CookiePolicy/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cors/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.Internal/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HostFiltering/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Html.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections.Common/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Features/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Results/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpLogging/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpOverrides/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpsPolicy/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Identity/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization.Routing/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Metadata/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ApiExplorer/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Core/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Cors/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.DataAnnotations/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Json/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Localization/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Razor/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.RazorPages/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.TagHelpers/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ViewFeatures/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.OutputCaching/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RateLimiting/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Runtime/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RequestDecompression/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCompression/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Rewrite/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.HttpSys/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IIS/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IISIntegration/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Core/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Session/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Common/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Core/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Protocols.Json/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticAssets/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticFiles/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebSockets/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebUtilities/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.CSharp/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Memory/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Abstractions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Binder.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.CommandLine.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Ini/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Json.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.KeyPerFile/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Xml/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Features/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Composite/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Embedded/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Physical.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileSystemGlobbing.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Abstractions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Http.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Core/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Stores/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Abstractions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Configuration.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Console.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Debug.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventLog.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventSource.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.TraceSource/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.ObjectPool/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.DataAnnotations/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Primitives.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Validation/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.WebEncoders/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.JSInterop/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Net.Http.Headers/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic.Core/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Registry/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "mscorlib/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/2.1.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Buffers/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Immutable/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Annotations/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.DataSetExtensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.DiagnosticSource/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.EventLog.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tracing/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Asn1/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Cbor/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Tar/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Brotli/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.AccessControl/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipelines/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes.AccessControl/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.AsyncEnumerable/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Memory/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Json/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.HttpListener/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Mail/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Quic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServerSentEvents/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServicePoint/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebClient/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebProxy/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics.Vectors/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.DispatchProxy/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Metadata/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.TypeExtensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.Unsafe/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.JavaScript/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Intrinsics/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Loader/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.AccessControl/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Cng/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.OpenSsl/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Xml/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Windows/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceProcess/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.CodePages/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encodings.Web/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Json/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.AccessControl/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Channels/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.RateLimiting/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Dataflow/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions.Local/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ValueTuple/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web.HttpUtility/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "WindowsBase/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "coverlet.collector/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-bJShQ6uWRTQ100ZeyiMqcFlhP7WJ+bCuabUs885dJiBEzMsJMSFr7BOyeCw4rgvQokteGi5rKQTlkhfQPUXg2A==", "path": "coverlet.collector/6.0.2", "hashPath": "coverlet.collector.6.0.2.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YIMO9T3JL8MeEXgVozKt2v79hquo/EFtnY0vgxmLnUvk1Rei/halI7kOWZL2RBeV9FMGzgM9LZA8CVaNwFMaNA==", "path": "microsoft.extensions.configuration/9.0.0", "hashPath": "microsoft.extensions.configuration.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "path": "microsoft.extensions.configuration.binder/9.0.0", "hashPath": "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qD+hdkBtR9Ps7AxfhTJCnoVakkadHgHlD1WRN0QHGHod+SDuca1ao1kF4G2rmpAz2AEKrE2N2vE8CCCZ+ILnNw==", "path": "microsoft.extensions.configuration.commandline/9.0.0", "hashPath": "microsoft.extensions.configuration.commandline.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-v5R638eNMxksfXb7MFnkPwLPp+Ym4W/SIGNuoe8qFVVyvygQD5DdLusybmYSJEr9zc1UzWzim/ATKeIOVvOFDg==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4EK93Jcd2lQG4GY6PAw8jGss0ZzFP0vPc1J85mES5fKNuDTqgFXHba9onBw2s18fs3I4vdo2AWyfD1mPAxWSQQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WiTK0LrnsqmedrbzwL7f4ZUo+/wByqy2eKab39I380i2rd8ImfCRMrtkqJVGDmfqlkP/YzhckVOwPc5MPrSNpg==", "path": "microsoft.extensions.configuration.json/9.0.0", "hashPath": "microsoft.extensions.configuration.json.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FShWw8OysquwV7wQHYkkz0VWsJSo6ETUu4h7tJRMtnG0uR+tzKOldhcO8xB1pGSOI3Ng6v3N1Q94YO8Rzq1P6A==", "path": "microsoft.extensions.configuration.usersecrets/9.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MCPrg7v3QgNMr0vX4vzRXvkNGgLg8vKWX0nKCWUxu2uPyMsaRgiRc1tHBnbTcfJMhMKj2slE/j2M9oGkd25DNw==", "path": "microsoft.extensions.dependencyinjection/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0CF9ZrNw5RAlRfbZuVIvzzhP8QeWqHiUmMBU/2H7Nmit8/vwP3/SbHeEctth7D4Gz2fBnEbokPc1NU8/j/1ZLw==", "path": "microsoft.extensions.diagnostics/9.0.0", "hashPath": "microsoft.extensions.diagnostics.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-************************************cpgiQNY/HlDAlnrhR9dvlURfFz428A+RTCJpUyB+aKTA6AgVcQ==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uK439QzYR0q2emLVtYzwyK3x+T5bTY4yWsd/k/ZUS9LR6Sflp8MIdhGXW8kQCd86dQD4tLqvcbLkku8qHY263Q==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3+ZUSpOSmie+o8NnLIRqCxSh65XL/ExU7JYnFOg58awDRlY3lVpZ9A369jkoZL1rpsq7LDhEfkn2ghhGaY1y5Q==", "path": "microsoft.extensions.fileproviders.physical/9.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jGFKZiXs2HNseK3NK/rfwHNNovER71jSj4BD1a/649ml9+h6oEtYd0GSALZDNW8jZ2Rh+oAeadOa6sagYW1F2A==", "path": "microsoft.extensions.filesystemglobbing/9.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wNmQWRCa83HYbpxQ3wH7xBn8oyGjONSj1k8svzrFUFyJMfg/Ja/g0NfI0p85wxlUxBh97A6ypmL8X5vVUA5y2Q==", "path": "microsoft.extensions.hosting/9.0.0", "hashPath": "microsoft.extensions.hosting.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yUKJgu81ExjvqbNWqZKshBbLntZMbMVz/P7Way2SBx7bMqA08Mfdc9O7hWDKAiSp+zPUGT6LKcSCQIPeDK+CCw==", "path": "microsoft.extensions.hosting.abstractions/9.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DqI4q54U4hH7bIAq9M5a/hl5Odr/KBAoaZ0dcT4OgutD8dook34CbkvAfAIzkMVjYXiL+E5ul9etwwqiX4PHGw==", "path": "microsoft.extensions.http/9.0.0", "hashPath": "microsoft.extensions.http.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-crj<PERSON>yORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "path": "microsoft.extensions.logging/9.0.0", "hashPath": "microsoft.extensions.logging.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "path": "microsoft.extensions.logging.abstractions/9.0.0", "hashPath": "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-H05HiqaNmg6GjH34ocYE9Wm1twm3Oz2aXZko8GTwGBzM7op2brpAA8pJ5yyD1OpS1mXUtModBYOlcZ/wXeWsSg==", "path": "microsoft.extensions.logging.configuration/9.0.0", "hashPath": "microsoft.extensions.logging.configuration.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yDZ4zsjl7N0K+R/1QTNpXBd79Kaf4qNLHtjk4NaG82UtNg2Z6etJywwv6OarOv3Rp7ocU7uIaRY4CrzHRO/d3w==", "path": "microsoft.extensions.logging.console/9.0.0", "hashPath": "microsoft.extensions.logging.console.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4wGlHsrLhYjLw4sFkfRixu2w4DK7dv60OjbvgbLGhUJk0eUPxYHhnszZ/P18nnAkfrPryvtOJ3ZTVev0kpqM6A==", "path": "microsoft.extensions.logging.debug/9.0.0", "hashPath": "microsoft.extensions.logging.debug.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/B8I5bScondnLMNULA3PBu/7Gvmv/P7L83j7gVrmLh6R+HCgHqUNIwVvzCok4ZjIXN2KxrsONHjFYwoBK5EJgQ==", "path": "microsoft.extensions.logging.eventlog/9.0.0", "hashPath": "microsoft.extensions.logging.eventlog.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zvSjdOAb3HW3aJPM5jf+PR9UoIkoci9id80RXmBgrDEozWI0GDw8tdmpyZgZSwFDvGCwHFodFLNQaeH8879rlA==", "path": "microsoft.extensions.logging.eventsource/9.0.0", "hashPath": "microsoft.extensions.logging.eventsource.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "path": "microsoft.extensions.options/9.0.0", "hashPath": "microsoft.extensions.options.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ob3FXsXkcSMQmGZi7qP07EQ39kZpSBlTcAZLbJLdI4FIf0Jug8biv2HTavWmnTirchctPlq9bl/26CXtQRguzA==", "path": "microsoft.extensions.options.configurationextensions/9.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "path": "microsoft.extensions.primitives/9.0.0", "hashPath": "microsoft.extensions.primitives.9.0.0.nupkg.sha512"}, "xunit.analyzers/1.16.0": {"type": "package", "serviceable": true, "sha512": "sha512-hptYM7vGr46GUIgZt21YHO4rfuBAQS2eINbFo16CV/Dqq+24Tp+P5gDCACu1AbFfW4Sp/WRfDPSK8fmUUb8s0Q==", "path": "xunit.analyzers/1.16.0", "hashPath": "xunit.analyzers.1.16.0.nupkg.sha512"}, "xunit.runner.visualstudio/2.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-vm1tbfXhFmjFMUmS4M0J0ASXz3/U5XvXBa6DOQUL3fEz4Vt6YPhv+ESCarx6M6D+9kJkJYZKCNvJMas1+nVfmQ==", "path": "xunit.runner.visualstudio/2.8.2", "hashPath": "xunit.runner.visualstudio.2.8.2.nupkg.sha512"}}}