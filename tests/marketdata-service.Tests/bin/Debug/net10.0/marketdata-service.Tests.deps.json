{"runtimeTarget": {"name": ".NETCoreApp,Version=v10.0", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "DEBUG", "NET", "NET10_0", "NETCOREAPP", "NET5_0_OR_GREATER", "NET6_0_OR_GREATER", "NET7_0_OR_GREATER", "NET8_0_OR_GREATER", "NET9_0_OR_GREATER", "NET10_0_OR_GREATER", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "languageVersion": "13.0", "platform": "", "allowUnsafe": false, "warningsAsErrors": false, "optimize": false, "keyFile": "", "emitEntryPoint": true, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETCoreApp,Version=v10.0": {"marketdata-service.Tests/1.0.0": {"dependencies": {"FluentAssertions": "6.12.2", "MarketDataService": "1.0.0", "Microsoft.AspNetCore.Mvc.Testing": "9.0.0", "Microsoft.EntityFrameworkCore.InMemory": "9.0.1", "Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.Http": "9.0.1", "Microsoft.NET.Test.Sdk": "17.12.0", "Moq": "4.20.72", "StackExchange.Redis.Extensions.Core": "10.2.0", "Testcontainers.Redis": "3.9.0", "coverlet.collector": "6.0.2", "xunit": "2.9.2", "xunit.runner.visualstudio": "2.8.2", "Microsoft.AspNetCore.Antiforgery": "10.0.0.0", "Microsoft.AspNetCore.Authentication.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Authentication.BearerToken": "10.0.0.0", "Microsoft.AspNetCore.Authentication.Cookies": "10.0.0.0", "Microsoft.AspNetCore.Authentication.Core": "10.0.0.0", "Microsoft.AspNetCore.Authentication": "10.0.0.0", "Microsoft.AspNetCore.Authentication.OAuth": "10.0.0.0", "Microsoft.AspNetCore.Authorization": "10.0.0.0", "Microsoft.AspNetCore.Authorization.Policy": "10.0.0.0", "Microsoft.AspNetCore.Components.Authorization": "10.0.0.0", "Microsoft.AspNetCore.Components": "10.0.0.0", "Microsoft.AspNetCore.Components.Endpoints": "10.0.0.0", "Microsoft.AspNetCore.Components.Forms": "10.0.0.0", "Microsoft.AspNetCore.Components.Server": "10.0.0.0", "Microsoft.AspNetCore.Components.Web": "10.0.0.0", "Microsoft.AspNetCore.Connections.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.CookiePolicy": "10.0.0.0", "Microsoft.AspNetCore.Cors": "10.0.0.0", "Microsoft.AspNetCore.Cryptography.Internal": "10.0.0.0", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "10.0.0.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.DataProtection": "10.0.0.0", "Microsoft.AspNetCore.DataProtection.Extensions": "10.0.0.0", "Microsoft.AspNetCore.Diagnostics.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Diagnostics": "10.0.0.0", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "10.0.0.0", "Microsoft.AspNetCore": "10.0.0.0", "Microsoft.AspNetCore.HostFiltering": "10.0.0.0", "Microsoft.AspNetCore.Hosting.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Hosting": "10.0.0.0", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Html.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Http.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Http.Connections.Common": "10.0.0.0", "Microsoft.AspNetCore.Http.Connections": "10.0.0.0", "Microsoft.AspNetCore.Http": "10.0.0.0", "Microsoft.AspNetCore.Http.Extensions": "10.0.0.0", "Microsoft.AspNetCore.Http.Features": "10.0.0.0", "Microsoft.AspNetCore.Http.Results": "10.0.0.0", "Microsoft.AspNetCore.HttpLogging": "10.0.0.0", "Microsoft.AspNetCore.HttpOverrides": "10.0.0.0", "Microsoft.AspNetCore.HttpsPolicy": "10.0.0.0", "Microsoft.AspNetCore.Identity": "10.0.0.0", "Microsoft.AspNetCore.Localization": "10.0.0.0", "Microsoft.AspNetCore.Localization.Routing": "10.0.0.0", "Microsoft.AspNetCore.Metadata": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Mvc.ApiExplorer": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Core": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Cors": "10.0.0.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "10.0.0.0", "Microsoft.AspNetCore.Mvc": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Localization": "10.0.0.0", "Microsoft.AspNetCore.Mvc.Razor": "10.0.0.0", "Microsoft.AspNetCore.Mvc.RazorPages": "10.0.0.0", "Microsoft.AspNetCore.Mvc.TagHelpers": "10.0.0.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "10.0.0.0", "Microsoft.AspNetCore.OutputCaching": "10.0.0.0", "Microsoft.AspNetCore.RateLimiting": "10.0.0.0", "Microsoft.AspNetCore.Razor": "10.0.0.0", "Microsoft.AspNetCore.Razor.Runtime": "10.0.0.0", "Microsoft.AspNetCore.RequestDecompression": "10.0.0.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.ResponseCaching": "10.0.0.0", "Microsoft.AspNetCore.ResponseCompression": "10.0.0.0", "Microsoft.AspNetCore.Rewrite": "10.0.0.0", "Microsoft.AspNetCore.Routing.Abstractions": "10.0.0.0", "Microsoft.AspNetCore.Routing": "10.0.0.0", "Microsoft.AspNetCore.Server.HttpSys": "10.0.0.0", "Microsoft.AspNetCore.Server.IIS": "10.0.0.0", "Microsoft.AspNetCore.Server.IISIntegration": "10.0.0.0", "Microsoft.AspNetCore.Server.Kestrel.Core": "10.0.0.0", "Microsoft.AspNetCore.Server.Kestrel": "10.0.0.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "10.0.0.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "10.0.0.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "10.0.0.0", "Microsoft.AspNetCore.Session": "10.0.0.0", "Microsoft.AspNetCore.SignalR.Common": "10.0.0.0", "Microsoft.AspNetCore.SignalR.Core": "10.0.0.0", "Microsoft.AspNetCore.SignalR": "10.0.0.0", "Microsoft.AspNetCore.SignalR.Protocols.Json": "10.0.0.0", "Microsoft.AspNetCore.StaticAssets": "10.0.0.0", "Microsoft.AspNetCore.StaticFiles": "10.0.0.0", "Microsoft.AspNetCore.WebSockets": "10.0.0.0", "Microsoft.AspNetCore.WebUtilities": "10.0.0.0", "Microsoft.CSharp": "10.0.0.0", "Microsoft.Extensions.Caching.Abstractions.Reference": "10.0.0.0", "Microsoft.Extensions.Caching.Memory.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.Abstractions.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.Binder.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.CommandLine.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.FileExtensions.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.Ini": "10.0.0.0", "Microsoft.Extensions.Configuration.Json.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.KeyPerFile": "10.0.0.0", "Microsoft.Extensions.Configuration.UserSecrets.Reference": "10.0.0.0", "Microsoft.Extensions.Configuration.Xml": "10.0.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions.Reference": "10.0.0.0", "Microsoft.Extensions.DependencyInjection.Reference": "10.0.0.0", "Microsoft.Extensions.Diagnostics.Abstractions.Reference": "10.0.0.0", "Microsoft.Extensions.Diagnostics.Reference": "10.0.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "10.0.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "10.0.0.0", "Microsoft.Extensions.Features": "10.0.0.0", "Microsoft.Extensions.FileProviders.Abstractions.Reference": "10.0.0.0", "Microsoft.Extensions.FileProviders.Composite": "10.0.0.0", "Microsoft.Extensions.FileProviders.Embedded": "10.0.0.0", "Microsoft.Extensions.FileProviders.Physical.Reference": "10.0.0.0", "Microsoft.Extensions.FileSystemGlobbing.Reference": "10.0.0.0", "Microsoft.Extensions.Hosting.Abstractions.Reference": "10.0.0.0", "Microsoft.Extensions.Hosting.Reference": "10.0.0.0", "Microsoft.Extensions.Http.Reference": "10.0.0.0", "Microsoft.Extensions.Identity.Core": "10.0.0.0", "Microsoft.Extensions.Identity.Stores": "10.0.0.0", "Microsoft.Extensions.Localization.Abstractions": "10.0.0.0", "Microsoft.Extensions.Localization": "10.0.0.0", "Microsoft.Extensions.Logging.Abstractions.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.Configuration.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.Console.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.Debug.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.EventLog.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.EventSource.Reference": "10.0.0.0", "Microsoft.Extensions.Logging.TraceSource": "10.0.0.0", "Microsoft.Extensions.ObjectPool": "10.0.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions.Reference": "10.0.0.0", "Microsoft.Extensions.Options.DataAnnotations": "10.0.0.0", "Microsoft.Extensions.Options.Reference": "10.0.0.0", "Microsoft.Extensions.Primitives.Reference": "10.0.0.0", "Microsoft.Extensions.Validation": "10.0.0.0", "Microsoft.Extensions.WebEncoders": "10.0.0.0", "Microsoft.JSInterop": "10.0.0.0", "Microsoft.Net.Http.Headers": "10.0.0.0", "Microsoft.VisualBasic.Core": "********", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives": "10.0.0.0", "Microsoft.Win32.Registry": "10.0.0.0", "mscorlib": "*******", "netstandard": "2.1.0.0", "System.AppContext": "10.0.0.0", "System.Buffers": "10.0.0.0", "System.Collections.Concurrent": "10.0.0.0", "System.Collections": "10.0.0.0", "System.Collections.Immutable": "10.0.0.0", "System.Collections.NonGeneric": "10.0.0.0", "System.Collections.Specialized": "10.0.0.0", "System.ComponentModel.Annotations": "10.0.0.0", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel": "10.0.0.0", "System.ComponentModel.EventBasedAsync": "10.0.0.0", "System.ComponentModel.Primitives": "10.0.0.0", "System.ComponentModel.TypeConverter": "10.0.0.0", "System.Configuration": "*******", "System.Console": "10.0.0.0", "System.Core": "*******", "System.Data.Common": "10.0.0.0", "System.Data.DataSetExtensions": "10.0.0.0", "System.Data": "*******", "System.Diagnostics.Contracts": "10.0.0.0", "System.Diagnostics.Debug": "10.0.0.0", "System.Diagnostics.DiagnosticSource": "10.0.0.0", "System.Diagnostics.EventLog.Reference": "10.0.0.0", "System.Diagnostics.FileVersionInfo": "10.0.0.0", "System.Diagnostics.Process": "10.0.0.0", "System.Diagnostics.StackTrace": "10.0.0.0", "System.Diagnostics.TextWriterTraceListener": "10.0.0.0", "System.Diagnostics.Tools": "10.0.0.0", "System.Diagnostics.TraceSource": "10.0.0.0", "System.Diagnostics.Tracing": "10.0.0.0", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "10.0.0.0", "System.Dynamic.Runtime": "10.0.0.0", "System.Formats.Asn1": "10.0.0.0", "System.Formats.Cbor": "10.0.0.0", "System.Formats.Tar": "10.0.0.0", "System.Globalization.Calendars": "10.0.0.0", "System.Globalization": "10.0.0.0", "System.Globalization.Extensions": "10.0.0.0", "System.IO.Compression.Brotli": "10.0.0.0", "System.IO.Compression": "10.0.0.0", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile": "10.0.0.0", "System.IO": "10.0.0.0", "System.IO.FileSystem.AccessControl": "10.0.0.0", "System.IO.FileSystem": "10.0.0.0", "System.IO.FileSystem.DriveInfo": "10.0.0.0", "System.IO.FileSystem.Primitives": "10.0.0.0", "System.IO.FileSystem.Watcher": "10.0.0.0", "System.IO.IsolatedStorage": "10.0.0.0", "System.IO.MemoryMappedFiles": "10.0.0.0", "System.IO.Pipelines": "10.0.0.0", "System.IO.Pipes.AccessControl": "10.0.0.0", "System.IO.Pipes": "10.0.0.0", "System.IO.UnmanagedMemoryStream": "10.0.0.0", "System.Linq.AsyncEnumerable": "10.0.0.0", "System.Linq": "10.0.0.0", "System.Linq.Expressions": "10.0.0.0", "System.Linq.Parallel": "10.0.0.0", "System.Linq.Queryable": "10.0.0.0", "System.Memory": "10.0.0.0", "System.Net": "*******", "System.Net.Http": "10.0.0.0", "System.Net.Http.Json": "10.0.0.0", "System.Net.HttpListener": "10.0.0.0", "System.Net.Mail": "10.0.0.0", "System.Net.NameResolution": "10.0.0.0", "System.Net.NetworkInformation": "10.0.0.0", "System.Net.Ping": "10.0.0.0", "System.Net.Primitives": "10.0.0.0", "System.Net.Quic": "10.0.0.0", "System.Net.Requests": "10.0.0.0", "System.Net.Security": "10.0.0.0", "System.Net.ServerSentEvents": "10.0.0.0", "System.Net.ServicePoint": "10.0.0.0", "System.Net.Sockets": "10.0.0.0", "System.Net.WebClient": "10.0.0.0", "System.Net.WebHeaderCollection": "10.0.0.0", "System.Net.WebProxy": "10.0.0.0", "System.Net.WebSockets.Client": "10.0.0.0", "System.Net.WebSockets": "10.0.0.0", "System.Numerics": "*******", "System.Numerics.Vectors": "10.0.0.0", "System.ObjectModel": "10.0.0.0", "System.Reflection.DispatchProxy": "10.0.0.0", "System.Reflection": "10.0.0.0", "System.Reflection.Emit": "10.0.0.0", "System.Reflection.Emit.ILGeneration": "10.0.0.0", "System.Reflection.Emit.Lightweight": "10.0.0.0", "System.Reflection.Extensions": "10.0.0.0", "System.Reflection.Metadata": "10.0.0.0", "System.Reflection.Primitives": "10.0.0.0", "System.Reflection.TypeExtensions": "10.0.0.0", "System.Resources.Reader": "10.0.0.0", "System.Resources.ResourceManager": "10.0.0.0", "System.Resources.Writer": "10.0.0.0", "System.Runtime.CompilerServices.Unsafe": "10.0.0.0", "System.Runtime.CompilerServices.VisualC": "10.0.0.0", "System.Runtime": "10.0.0.0", "System.Runtime.Extensions": "10.0.0.0", "System.Runtime.Handles": "10.0.0.0", "System.Runtime.InteropServices": "10.0.0.0", "System.Runtime.InteropServices.JavaScript": "10.0.0.0", "System.Runtime.InteropServices.RuntimeInformation": "10.0.0.0", "System.Runtime.Intrinsics": "10.0.0.0", "System.Runtime.Loader": "10.0.0.0", "System.Runtime.Numerics": "10.0.0.0", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "10.0.0.0", "System.Runtime.Serialization.Primitives": "10.0.0.0", "System.Runtime.Serialization.Xml": "10.0.0.0", "System.Security.AccessControl": "10.0.0.0", "System.Security.Claims": "10.0.0.0", "System.Security.Cryptography.Algorithms": "10.0.0.0", "System.Security.Cryptography.Cng": "10.0.0.0", "System.Security.Cryptography.Csp": "10.0.0.0", "System.Security.Cryptography": "10.0.0.0", "System.Security.Cryptography.Encoding": "10.0.0.0", "System.Security.Cryptography.OpenSsl": "10.0.0.0", "System.Security.Cryptography.Primitives": "10.0.0.0", "System.Security.Cryptography.X509Certificates": "10.0.0.0", "System.Security.Cryptography.Xml": "10.0.0.0", "System.Security": "*******", "System.Security.Principal": "10.0.0.0", "System.Security.Principal.Windows": "10.0.0.0", "System.Security.SecureString": "10.0.0.0", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages": "10.0.0.0", "System.Text.Encoding": "10.0.0.0", "System.Text.Encoding.Extensions": "10.0.0.0", "System.Text.Encodings.Web": "10.0.0.0", "System.Text.Json": "10.0.0.0", "System.Text.RegularExpressions": "10.0.0.0", "System.Threading.AccessControl": "10.0.0.0", "System.Threading.Channels": "10.0.0.0", "System.Threading": "10.0.0.0", "System.Threading.Overlapped": "10.0.0.0", "System.Threading.RateLimiting": "10.0.0.0", "System.Threading.Tasks.Dataflow": "10.0.0.0", "System.Threading.Tasks": "10.0.0.0", "System.Threading.Tasks.Extensions": "10.0.0.0", "System.Threading.Tasks.Parallel": "10.0.0.0", "System.Threading.Thread": "10.0.0.0", "System.Threading.ThreadPool": "10.0.0.0", "System.Threading.Timer": "10.0.0.0", "System.Transactions": "*******", "System.Transactions.Local": "10.0.0.0", "System.ValueTuple": "10.0.0.0", "System.Web": "*******", "System.Web.HttpUtility": "10.0.0.0", "System.Windows": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter": "10.0.0.0", "System.Xml.Serialization": "*******", "System.Xml.XDocument": "10.0.0.0", "System.Xml.XmlDocument": "10.0.0.0", "System.Xml.XmlSerializer": "10.0.0.0", "System.Xml.XPath": "10.0.0.0", "System.Xml.XPath.XDocument": "10.0.0.0", "WindowsBase": "*******"}, "runtime": {"marketdata-service.Tests.dll": {}}, "compile": {"marketdata-service.Tests.dll": {}}}, "Castle.Core/5.1.1": {"dependencies": {"System.Diagnostics.EventLog": "9.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.1.0"}}, "compile": {"lib/net6.0/Castle.Core.dll": {}}}, "Docker.DotNet/3.125.15": {"dependencies": {"Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.1/Docker.DotNet.dll": {"assemblyVersion": "3.125.0.0", "fileVersion": "3.125.15.1"}}, "compile": {"lib/netstandard2.1/Docker.DotNet.dll": {}}}, "Docker.DotNet.X509/3.125.15": {"dependencies": {"Docker.DotNet": "3.125.15"}, "runtime": {"lib/netstandard2.1/Docker.DotNet.X509.dll": {"assemblyVersion": "3.125.0.0", "fileVersion": "3.125.15.1"}}, "compile": {"lib/netstandard2.1/Docker.DotNet.X509.dll": {}}}, "DotNetEnv/3.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "NETStandard.Library": "1.6.1", "Sprache": "2.3.1"}, "runtime": {"lib/netstandard1.3/DotNetEnv.dll": {"assemblyVersion": "3.1.1.0", "fileVersion": "3.1.1.0"}}, "compile": {"lib/netstandard1.3/DotNetEnv.dll": {}}}, "FluentAssertions/6.12.2": {"dependencies": {"System.Configuration.ConfigurationManager": "4.4.0"}, "runtime": {"lib/net6.0/FluentAssertions.dll": {"assemblyVersion": "6.12.2.0", "fileVersion": "6.12.2.0"}}, "compile": {"lib/net6.0/FluentAssertions.dll": {}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}, "compile": {"lib/net6.0/Humanizer.dll": {}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {}}}, "Microsoft.AspNetCore.Mvc.Testing/9.0.0": {"dependencies": {"Microsoft.AspNetCore.TestHost": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.1", "Microsoft.Extensions.Hosting": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Mvc.Testing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.Mvc.Testing.dll": {}}}, "Microsoft.AspNetCore.OpenApi/9.0.0": {"dependencies": {"Microsoft.OpenApi": "1.6.22"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {}}}, "Microsoft.AspNetCore.TestHost/9.0.0": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.TestHost.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}, "compile": {"lib/net9.0/Microsoft.AspNetCore.TestHost.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.Build.Locator/1.7.8": {"runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {"assemblyVersion": "*******", "fileVersion": "1.7.8.28074"}}, "compile": {"lib/net6.0/Microsoft.Build.Locator.dll": {}}}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"dependencies": {"Microsoft.Build.Framework": "17.8.3", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {}, "lib/net7.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {}}}, "Microsoft.CodeCoverage/17.12.0": {"runtime": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.524.48002"}}, "compile": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {}}}, "Microsoft.EntityFrameworkCore/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.1", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.1": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}}, "Microsoft.EntityFrameworkCore.Design/9.0.1": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build.Framework": "17.8.3", "Microsoft.Build.Locator": "1.7.8", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.8.0", "Microsoft.EntityFrameworkCore.Relational": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyModel": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Mono.TextTemplating": "3.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {}}}, "Microsoft.EntityFrameworkCore.InMemory/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.InMemory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.InMemory.dll": {}}}, "Microsoft.EntityFrameworkCore.Relational/9.0.1": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.Extensions.Caching.Memory": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61002"}}, "compile": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {}}}, "Microsoft.Extensions.DependencyModel/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "9.0.0.1", "fileVersion": "9.0.124.61010"}}, "compile": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {}}}, "Microsoft.IdentityModel.Logging/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {}}}, "Microsoft.IdentityModel.Tokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}, "compile": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {}}}, "Microsoft.NET.Test.Sdk/17.12.0": {"dependencies": {"Microsoft.CodeCoverage": "17.12.0", "Microsoft.TestPlatform.TestHost": "17.12.0"}}, "Microsoft.OpenApi/1.6.22": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}}, "Microsoft.TestPlatform.ObjectModel/17.12.0": {"runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}}}, "Microsoft.TestPlatform.TestHost/17.12.0": {"dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.12.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}, "lib/netcoreapp3.1/testhost.dll": {"assemblyVersion": "********", "fileVersion": "17.1200.24.56501"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}, "compile": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {}, "lib/netcoreapp3.1/testhost.dll": {}}}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/Mono.TextTemplating.dll": {}}}, "Moq/4.20.72": {"dependencies": {"Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Moq.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "compile": {"lib/net6.0/Moq.dll": {}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.1.25517"}}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {}}}, "Npgsql/9.0.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.1"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Npgsql.dll": {}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Relational": "9.0.1", "Npgsql": "9.0.3"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {}}}, "Pipelines.Sockets.Unofficial/2.2.8": {"runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.8.1080"}}, "compile": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {}}}, "SharpZipLib/1.4.2": {"runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {}}}, "Sprache/2.3.1": {"runtime": {"lib/netstandard2.1/Sprache.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.1/Sprache.dll": {}}}, "SSH.NET/2023.0.0": {"dependencies": {"SshNet.Security.Cryptography": "1.3.0"}, "runtime": {"lib/net7.0/Renci.SshNet.dll": {"assemblyVersion": "202*******", "fileVersion": "202*******"}}, "compile": {"lib/net7.0/Renci.SshNet.dll": {}}}, "SshNet.Security.Cryptography/1.3.0": {"runtime": {"lib/netstandard2.0/SshNet.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/SshNet.Security.Cryptography.dll": {}}}, "StackExchange.Redis/2.8.16": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Pipelines.Sockets.Unofficial": "2.2.8"}, "runtime": {"lib/net6.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.8.16.12844"}}, "compile": {"lib/net6.0/StackExchange.Redis.dll": {}}}, "StackExchange.Redis.Extensions.Core/10.2.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.1", "StackExchange.Redis": "2.8.16"}, "runtime": {"lib/net8.0/StackExchange.Redis.Extensions.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/net8.0/StackExchange.Redis.Extensions.Core.dll": {}}}, "Swashbuckle.AspNetCore/7.2.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "7.2.0", "Swashbuckle.AspNetCore.SwaggerGen": "7.2.0", "Swashbuckle.AspNetCore.SwaggerUI": "7.2.0"}}, "Swashbuckle.AspNetCore.Swagger/7.2.0": {"dependencies": {"Microsoft.OpenApi": "1.6.22"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "7.2.0.956"}}, "compile": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/7.2.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "7.2.0"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "7.2.0.956"}}, "compile": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/7.2.0": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "7.2.0.956"}}, "compile": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/net6.0/System.CodeDom.dll": {}}}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "compile": {"lib/net7.0/System.Composition.AttributedModel.dll": {}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "compile": {"lib/net7.0/System.Composition.Convention.dll": {}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "compile": {"lib/net7.0/System.Composition.Hosting.dll": {}}}, "System.Composition.Runtime/7.0.0": {"runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "compile": {"lib/net7.0/System.Composition.Runtime.dll": {}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "compile": {"lib/net7.0/System.Composition.TypedParts.dll": {}}}, "System.Configuration.ConfigurationManager/4.4.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.4.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}, "compile": {"ref/netstandard2.0/System.Configuration.ConfigurationManager.dll": {}}}, "System.Diagnostics.EventLog/9.0.0": {"runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}, "compile": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {}}}, "System.Security.Cryptography.ProtectedData/4.4.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "Testcontainers/3.9.0": {"dependencies": {"Docker.DotNet": "3.125.15", "Docker.DotNet.X509": "3.125.15", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "SSH.NET": "2023.0.0", "SharpZipLib": "1.4.2"}, "runtime": {"lib/net8.0/Testcontainers.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Testcontainers.dll": {}}}, "Testcontainers.Redis/3.9.0": {"dependencies": {"Testcontainers": "3.9.0"}, "runtime": {"lib/net8.0/Testcontainers.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net8.0/Testcontainers.Redis.dll": {}}}, "xunit/2.9.2": {"dependencies": {"xunit.analyzers": "1.16.0", "xunit.assert": "2.9.2", "xunit.core": "2.9.2"}}, "xunit.abstractions/2.0.3": {"runtime": {"lib/netstandard2.0/xunit.abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}}, "compile": {"lib/netstandard2.0/xunit.abstractions.dll": {}}}, "xunit.assert/2.9.2": {"runtime": {"lib/net6.0/xunit.assert.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/net6.0/xunit.assert.dll": {}}}, "xunit.core/2.9.2": {"dependencies": {"xunit.extensibility.core": "2.9.2", "xunit.extensibility.execution": "2.9.2"}}, "xunit.extensibility.core/2.9.2": {"dependencies": {"xunit.abstractions": "2.0.3"}, "runtime": {"lib/netstandard1.1/xunit.core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard1.1/xunit.core.dll": {}}}, "xunit.extensibility.execution/2.9.2": {"dependencies": {"xunit.extensibility.core": "2.9.2"}, "runtime": {"lib/netstandard1.1/xunit.execution.dotnet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard1.1/xunit.execution.dotnet.dll": {}}}, "MarketDataService/1.0.0": {"dependencies": {"DotNetEnv": "3.1.1", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.0", "Microsoft.AspNetCore.OpenApi": "9.0.0", "Microsoft.EntityFrameworkCore": "9.0.1", "Microsoft.EntityFrameworkCore.Design": "9.0.1", "Npgsql.EntityFrameworkCore.PostgreSQL": "9.0.4", "StackExchange.Redis": "2.8.16", "Swashbuckle.AspNetCore": "7.2.0"}, "runtime": {"MarketDataService.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"MarketDataService.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Antiforgery.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.BearerToken/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authentication.BearerToken.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Cookies/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authentication.Cookies.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Core/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authentication.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authentication.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OAuth/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authentication.OAuth.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Policy/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Authorization.Policy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Authorization/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Components.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Components.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Endpoints/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Components.Endpoints.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Forms/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Components.Forms.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Server/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Components.Server.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Web/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Components.Web.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Connections.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Connections.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.CookiePolicy/10.0.0.0": {"compile": {"Microsoft.AspNetCore.CookiePolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cors/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.Internal/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Cryptography.Internal.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection/10.0.0.0": {"compile": {"Microsoft.AspNetCore.DataProtection.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Extensions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore/10.0.0.0": {"compile": {"Microsoft.AspNetCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HostFiltering/10.0.0.0": {"compile": {"Microsoft.AspNetCore.HostFiltering.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Html.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections.Common/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.Connections.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.Connections.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Extensions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Features/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.Features.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Results/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Http.Results.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpLogging/10.0.0.0": {"compile": {"Microsoft.AspNetCore.HttpLogging.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpOverrides/10.0.0.0": {"compile": {"Microsoft.AspNetCore.HttpOverrides.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpsPolicy/10.0.0.0": {"compile": {"Microsoft.AspNetCore.HttpsPolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Identity.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization.Routing/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Localization.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Metadata/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Metadata.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ApiExplorer/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Core/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Cors/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.DataAnnotations/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Json/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Localization/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.RazorPages/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.TagHelpers/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ViewFeatures/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.OutputCaching/10.0.0.0": {"compile": {"Microsoft.AspNetCore.OutputCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RateLimiting/10.0.0.0": {"compile": {"Microsoft.AspNetCore.RateLimiting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Runtime/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.RequestDecompression/10.0.0.0": {"compile": {"Microsoft.AspNetCore.RequestDecompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching/10.0.0.0": {"compile": {"Microsoft.AspNetCore.ResponseCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCompression/10.0.0.0": {"compile": {"Microsoft.AspNetCore.ResponseCompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Rewrite/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Rewrite.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Abstractions/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.HttpSys/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.HttpSys.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IIS/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.IIS.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IISIntegration/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Core/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Session/10.0.0.0": {"compile": {"Microsoft.AspNetCore.Session.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Common/10.0.0.0": {"compile": {"Microsoft.AspNetCore.SignalR.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Core/10.0.0.0": {"compile": {"Microsoft.AspNetCore.SignalR.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR/10.0.0.0": {"compile": {"Microsoft.AspNetCore.SignalR.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Protocols.Json/10.0.0.0": {"compile": {"Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticAssets/10.0.0.0": {"compile": {"Microsoft.AspNetCore.StaticAssets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticFiles/10.0.0.0": {"compile": {"Microsoft.AspNetCore.StaticFiles.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebSockets/10.0.0.0": {"compile": {"Microsoft.AspNetCore.WebSockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebUtilities/10.0.0.0": {"compile": {"Microsoft.AspNetCore.WebUtilities.dll": {}}, "compileOnly": true}, "Microsoft.CSharp/10.0.0.0": {"compile": {"Microsoft.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Abstractions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Caching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Caching.Memory.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Caching.Memory.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Abstractions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Binder.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.Binder.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.CommandLine.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.FileExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Ini/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.Ini.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Json.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.Json.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.KeyPerFile/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.UserSecrets.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Xml/10.0.0.0": {"compile": {"Microsoft.Extensions.Configuration.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.DependencyInjection.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/10.0.0.0": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks/10.0.0.0": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Features/10.0.0.0": {"compile": {"Microsoft.Extensions.Features.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.FileProviders.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Composite/10.0.0.0": {"compile": {"Microsoft.Extensions.FileProviders.Composite.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Embedded/10.0.0.0": {"compile": {"Microsoft.Extensions.FileProviders.Embedded.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Physical.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.FileProviders.Physical.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileSystemGlobbing.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.FileSystemGlobbing.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Abstractions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Http.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Http.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Core/10.0.0.0": {"compile": {"Microsoft.Extensions.Identity.Core.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Stores/10.0.0.0": {"compile": {"Microsoft.Extensions.Identity.Stores.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization.Abstractions/10.0.0.0": {"compile": {"Microsoft.Extensions.Localization.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization/10.0.0.0": {"compile": {"Microsoft.Extensions.Localization.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Abstractions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Configuration.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.Console.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.Debug.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventLog.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.EventLog.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.EventSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.TraceSource/10.0.0.0": {"compile": {"Microsoft.Extensions.Logging.TraceSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.ObjectPool/10.0.0.0": {"compile": {"Microsoft.Extensions.ObjectPool.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.DataAnnotations/10.0.0.0": {"compile": {"Microsoft.Extensions.Options.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Options.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Primitives.Reference/10.0.0.0": {"compile": {"Microsoft.Extensions.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Validation/10.0.0.0": {"compile": {"Microsoft.Extensions.Validation.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.WebEncoders/10.0.0.0": {"compile": {"Microsoft.Extensions.WebEncoders.dll": {}}, "compileOnly": true}, "Microsoft.JSInterop/10.0.0.0": {"compile": {"Microsoft.JSInterop.dll": {}}, "compileOnly": true}, "Microsoft.Net.Http.Headers/10.0.0.0": {"compile": {"Microsoft.Net.Http.Headers.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic.Core/********": {"compile": {"Microsoft.VisualBasic.Core.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic/10.0.0.0": {"compile": {"Microsoft.VisualBasic.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Primitives/10.0.0.0": {"compile": {"Microsoft.Win32.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Registry/10.0.0.0": {"compile": {"Microsoft.Win32.Registry.dll": {}}, "compileOnly": true}, "mscorlib/*******": {"compile": {"mscorlib.dll": {}}, "compileOnly": true}, "netstandard/2.1.0.0": {"compile": {"netstandard.dll": {}}, "compileOnly": true}, "System.AppContext/10.0.0.0": {"compile": {"System.AppContext.dll": {}}, "compileOnly": true}, "System.Buffers/10.0.0.0": {"compile": {"System.Buffers.dll": {}}, "compileOnly": true}, "System.Collections.Concurrent/10.0.0.0": {"compile": {"System.Collections.Concurrent.dll": {}}, "compileOnly": true}, "System.Collections/10.0.0.0": {"compile": {"System.Collections.dll": {}}, "compileOnly": true}, "System.Collections.Immutable/10.0.0.0": {"compile": {"System.Collections.Immutable.dll": {}}, "compileOnly": true}, "System.Collections.NonGeneric/10.0.0.0": {"compile": {"System.Collections.NonGeneric.dll": {}}, "compileOnly": true}, "System.Collections.Specialized/10.0.0.0": {"compile": {"System.Collections.Specialized.dll": {}}, "compileOnly": true}, "System.ComponentModel.Annotations/10.0.0.0": {"compile": {"System.ComponentModel.Annotations.dll": {}}, "compileOnly": true}, "System.ComponentModel.DataAnnotations/*******": {"compile": {"System.ComponentModel.DataAnnotations.dll": {}}, "compileOnly": true}, "System.ComponentModel/10.0.0.0": {"compile": {"System.ComponentModel.dll": {}}, "compileOnly": true}, "System.ComponentModel.EventBasedAsync/10.0.0.0": {"compile": {"System.ComponentModel.EventBasedAsync.dll": {}}, "compileOnly": true}, "System.ComponentModel.Primitives/10.0.0.0": {"compile": {"System.ComponentModel.Primitives.dll": {}}, "compileOnly": true}, "System.ComponentModel.TypeConverter/10.0.0.0": {"compile": {"System.ComponentModel.TypeConverter.dll": {}}, "compileOnly": true}, "System.Configuration/*******": {"compile": {"System.Configuration.dll": {}}, "compileOnly": true}, "System.Console/10.0.0.0": {"compile": {"System.Console.dll": {}}, "compileOnly": true}, "System.Core/*******": {"compile": {"System.Core.dll": {}}, "compileOnly": true}, "System.Data.Common/10.0.0.0": {"compile": {"System.Data.Common.dll": {}}, "compileOnly": true}, "System.Data.DataSetExtensions/10.0.0.0": {"compile": {"System.Data.DataSetExtensions.dll": {}}, "compileOnly": true}, "System.Data/*******": {"compile": {"System.Data.dll": {}}, "compileOnly": true}, "System.Diagnostics.Contracts/10.0.0.0": {"compile": {"System.Diagnostics.Contracts.dll": {}}, "compileOnly": true}, "System.Diagnostics.Debug/10.0.0.0": {"compile": {"System.Diagnostics.Debug.dll": {}}, "compileOnly": true}, "System.Diagnostics.DiagnosticSource/10.0.0.0": {"compile": {"System.Diagnostics.DiagnosticSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.EventLog.Reference/10.0.0.0": {"compile": {"System.Diagnostics.EventLog.dll": {}}, "compileOnly": true}, "System.Diagnostics.FileVersionInfo/10.0.0.0": {"compile": {"System.Diagnostics.FileVersionInfo.dll": {}}, "compileOnly": true}, "System.Diagnostics.Process/10.0.0.0": {"compile": {"System.Diagnostics.Process.dll": {}}, "compileOnly": true}, "System.Diagnostics.StackTrace/10.0.0.0": {"compile": {"System.Diagnostics.StackTrace.dll": {}}, "compileOnly": true}, "System.Diagnostics.TextWriterTraceListener/10.0.0.0": {"compile": {"System.Diagnostics.TextWriterTraceListener.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tools/10.0.0.0": {"compile": {"System.Diagnostics.Tools.dll": {}}, "compileOnly": true}, "System.Diagnostics.TraceSource/10.0.0.0": {"compile": {"System.Diagnostics.TraceSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tracing/10.0.0.0": {"compile": {"System.Diagnostics.Tracing.dll": {}}, "compileOnly": true}, "System/*******": {"compile": {"System.dll": {}}, "compileOnly": true}, "System.Drawing/*******": {"compile": {"System.Drawing.dll": {}}, "compileOnly": true}, "System.Drawing.Primitives/10.0.0.0": {"compile": {"System.Drawing.Primitives.dll": {}}, "compileOnly": true}, "System.Dynamic.Runtime/10.0.0.0": {"compile": {"System.Dynamic.Runtime.dll": {}}, "compileOnly": true}, "System.Formats.Asn1/10.0.0.0": {"compile": {"System.Formats.Asn1.dll": {}}, "compileOnly": true}, "System.Formats.Cbor/10.0.0.0": {"compile": {"System.Formats.Cbor.dll": {}}, "compileOnly": true}, "System.Formats.Tar/10.0.0.0": {"compile": {"System.Formats.Tar.dll": {}}, "compileOnly": true}, "System.Globalization.Calendars/10.0.0.0": {"compile": {"System.Globalization.Calendars.dll": {}}, "compileOnly": true}, "System.Globalization/10.0.0.0": {"compile": {"System.Globalization.dll": {}}, "compileOnly": true}, "System.Globalization.Extensions/10.0.0.0": {"compile": {"System.Globalization.Extensions.dll": {}}, "compileOnly": true}, "System.IO.Compression.Brotli/10.0.0.0": {"compile": {"System.IO.Compression.Brotli.dll": {}}, "compileOnly": true}, "System.IO.Compression/10.0.0.0": {"compile": {"System.IO.Compression.dll": {}}, "compileOnly": true}, "System.IO.Compression.FileSystem/*******": {"compile": {"System.IO.Compression.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.Compression.ZipFile/10.0.0.0": {"compile": {"System.IO.Compression.ZipFile.dll": {}}, "compileOnly": true}, "System.IO/10.0.0.0": {"compile": {"System.IO.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.AccessControl/10.0.0.0": {"compile": {"System.IO.FileSystem.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.FileSystem/10.0.0.0": {"compile": {"System.IO.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.DriveInfo/10.0.0.0": {"compile": {"System.IO.FileSystem.DriveInfo.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Primitives/10.0.0.0": {"compile": {"System.IO.FileSystem.Primitives.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Watcher/10.0.0.0": {"compile": {"System.IO.FileSystem.Watcher.dll": {}}, "compileOnly": true}, "System.IO.IsolatedStorage/10.0.0.0": {"compile": {"System.IO.IsolatedStorage.dll": {}}, "compileOnly": true}, "System.IO.MemoryMappedFiles/10.0.0.0": {"compile": {"System.IO.MemoryMappedFiles.dll": {}}, "compileOnly": true}, "System.IO.Pipelines/10.0.0.0": {"compile": {"System.IO.Pipelines.dll": {}}, "compileOnly": true}, "System.IO.Pipes.AccessControl/10.0.0.0": {"compile": {"System.IO.Pipes.AccessControl.dll": {}}, "compileOnly": true}, "System.IO.Pipes/10.0.0.0": {"compile": {"System.IO.Pipes.dll": {}}, "compileOnly": true}, "System.IO.UnmanagedMemoryStream/10.0.0.0": {"compile": {"System.IO.UnmanagedMemoryStream.dll": {}}, "compileOnly": true}, "System.Linq.AsyncEnumerable/10.0.0.0": {"compile": {"System.Linq.AsyncEnumerable.dll": {}}, "compileOnly": true}, "System.Linq/10.0.0.0": {"compile": {"System.Linq.dll": {}}, "compileOnly": true}, "System.Linq.Expressions/10.0.0.0": {"compile": {"System.Linq.Expressions.dll": {}}, "compileOnly": true}, "System.Linq.Parallel/10.0.0.0": {"compile": {"System.Linq.Parallel.dll": {}}, "compileOnly": true}, "System.Linq.Queryable/10.0.0.0": {"compile": {"System.Linq.Queryable.dll": {}}, "compileOnly": true}, "System.Memory/10.0.0.0": {"compile": {"System.Memory.dll": {}}, "compileOnly": true}, "System.Net/*******": {"compile": {"System.Net.dll": {}}, "compileOnly": true}, "System.Net.Http/10.0.0.0": {"compile": {"System.Net.Http.dll": {}}, "compileOnly": true}, "System.Net.Http.Json/10.0.0.0": {"compile": {"System.Net.Http.Json.dll": {}}, "compileOnly": true}, "System.Net.HttpListener/10.0.0.0": {"compile": {"System.Net.HttpListener.dll": {}}, "compileOnly": true}, "System.Net.Mail/10.0.0.0": {"compile": {"System.Net.Mail.dll": {}}, "compileOnly": true}, "System.Net.NameResolution/10.0.0.0": {"compile": {"System.Net.NameResolution.dll": {}}, "compileOnly": true}, "System.Net.NetworkInformation/10.0.0.0": {"compile": {"System.Net.NetworkInformation.dll": {}}, "compileOnly": true}, "System.Net.Ping/10.0.0.0": {"compile": {"System.Net.Ping.dll": {}}, "compileOnly": true}, "System.Net.Primitives/10.0.0.0": {"compile": {"System.Net.Primitives.dll": {}}, "compileOnly": true}, "System.Net.Quic/10.0.0.0": {"compile": {"System.Net.Quic.dll": {}}, "compileOnly": true}, "System.Net.Requests/10.0.0.0": {"compile": {"System.Net.Requests.dll": {}}, "compileOnly": true}, "System.Net.Security/10.0.0.0": {"compile": {"System.Net.Security.dll": {}}, "compileOnly": true}, "System.Net.ServerSentEvents/10.0.0.0": {"compile": {"System.Net.ServerSentEvents.dll": {}}, "compileOnly": true}, "System.Net.ServicePoint/10.0.0.0": {"compile": {"System.Net.ServicePoint.dll": {}}, "compileOnly": true}, "System.Net.Sockets/10.0.0.0": {"compile": {"System.Net.Sockets.dll": {}}, "compileOnly": true}, "System.Net.WebClient/10.0.0.0": {"compile": {"System.Net.WebClient.dll": {}}, "compileOnly": true}, "System.Net.WebHeaderCollection/10.0.0.0": {"compile": {"System.Net.WebHeaderCollection.dll": {}}, "compileOnly": true}, "System.Net.WebProxy/10.0.0.0": {"compile": {"System.Net.WebProxy.dll": {}}, "compileOnly": true}, "System.Net.WebSockets.Client/10.0.0.0": {"compile": {"System.Net.WebSockets.Client.dll": {}}, "compileOnly": true}, "System.Net.WebSockets/10.0.0.0": {"compile": {"System.Net.WebSockets.dll": {}}, "compileOnly": true}, "System.Numerics/*******": {"compile": {"System.Numerics.dll": {}}, "compileOnly": true}, "System.Numerics.Vectors/10.0.0.0": {"compile": {"System.Numerics.Vectors.dll": {}}, "compileOnly": true}, "System.ObjectModel/10.0.0.0": {"compile": {"System.ObjectModel.dll": {}}, "compileOnly": true}, "System.Reflection.DispatchProxy/10.0.0.0": {"compile": {"System.Reflection.DispatchProxy.dll": {}}, "compileOnly": true}, "System.Reflection/10.0.0.0": {"compile": {"System.Reflection.dll": {}}, "compileOnly": true}, "System.Reflection.Emit/10.0.0.0": {"compile": {"System.Reflection.Emit.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.ILGeneration/10.0.0.0": {"compile": {"System.Reflection.Emit.ILGeneration.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Lightweight/10.0.0.0": {"compile": {"System.Reflection.Emit.Lightweight.dll": {}}, "compileOnly": true}, "System.Reflection.Extensions/10.0.0.0": {"compile": {"System.Reflection.Extensions.dll": {}}, "compileOnly": true}, "System.Reflection.Metadata/10.0.0.0": {"compile": {"System.Reflection.Metadata.dll": {}}, "compileOnly": true}, "System.Reflection.Primitives/10.0.0.0": {"compile": {"System.Reflection.Primitives.dll": {}}, "compileOnly": true}, "System.Reflection.TypeExtensions/10.0.0.0": {"compile": {"System.Reflection.TypeExtensions.dll": {}}, "compileOnly": true}, "System.Resources.Reader/10.0.0.0": {"compile": {"System.Resources.Reader.dll": {}}, "compileOnly": true}, "System.Resources.ResourceManager/10.0.0.0": {"compile": {"System.Resources.ResourceManager.dll": {}}, "compileOnly": true}, "System.Resources.Writer/10.0.0.0": {"compile": {"System.Resources.Writer.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.Unsafe/10.0.0.0": {"compile": {"System.Runtime.CompilerServices.Unsafe.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.VisualC/10.0.0.0": {"compile": {"System.Runtime.CompilerServices.VisualC.dll": {}}, "compileOnly": true}, "System.Runtime/10.0.0.0": {"compile": {"System.Runtime.dll": {}}, "compileOnly": true}, "System.Runtime.Extensions/10.0.0.0": {"compile": {"System.Runtime.Extensions.dll": {}}, "compileOnly": true}, "System.Runtime.Handles/10.0.0.0": {"compile": {"System.Runtime.Handles.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices/10.0.0.0": {"compile": {"System.Runtime.InteropServices.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.JavaScript/10.0.0.0": {"compile": {"System.Runtime.InteropServices.JavaScript.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.RuntimeInformation/10.0.0.0": {"compile": {"System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "compileOnly": true}, "System.Runtime.Intrinsics/10.0.0.0": {"compile": {"System.Runtime.Intrinsics.dll": {}}, "compileOnly": true}, "System.Runtime.Loader/10.0.0.0": {"compile": {"System.Runtime.Loader.dll": {}}, "compileOnly": true}, "System.Runtime.Numerics/10.0.0.0": {"compile": {"System.Runtime.Numerics.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization/*******": {"compile": {"System.Runtime.Serialization.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Formatters/*******": {"compile": {"System.Runtime.Serialization.Formatters.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Json/10.0.0.0": {"compile": {"System.Runtime.Serialization.Json.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Primitives/10.0.0.0": {"compile": {"System.Runtime.Serialization.Primitives.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Xml/10.0.0.0": {"compile": {"System.Runtime.Serialization.Xml.dll": {}}, "compileOnly": true}, "System.Security.AccessControl/10.0.0.0": {"compile": {"System.Security.AccessControl.dll": {}}, "compileOnly": true}, "System.Security.Claims/10.0.0.0": {"compile": {"System.Security.Claims.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Algorithms/10.0.0.0": {"compile": {"System.Security.Cryptography.Algorithms.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Cng/10.0.0.0": {"compile": {"System.Security.Cryptography.Cng.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Csp/10.0.0.0": {"compile": {"System.Security.Cryptography.Csp.dll": {}}, "compileOnly": true}, "System.Security.Cryptography/10.0.0.0": {"compile": {"System.Security.Cryptography.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Encoding/10.0.0.0": {"compile": {"System.Security.Cryptography.Encoding.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.OpenSsl/10.0.0.0": {"compile": {"System.Security.Cryptography.OpenSsl.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Primitives/10.0.0.0": {"compile": {"System.Security.Cryptography.Primitives.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.X509Certificates/10.0.0.0": {"compile": {"System.Security.Cryptography.X509Certificates.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Xml/10.0.0.0": {"compile": {"System.Security.Cryptography.Xml.dll": {}}, "compileOnly": true}, "System.Security/*******": {"compile": {"System.Security.dll": {}}, "compileOnly": true}, "System.Security.Principal/10.0.0.0": {"compile": {"System.Security.Principal.dll": {}}, "compileOnly": true}, "System.Security.Principal.Windows/10.0.0.0": {"compile": {"System.Security.Principal.Windows.dll": {}}, "compileOnly": true}, "System.Security.SecureString/10.0.0.0": {"compile": {"System.Security.SecureString.dll": {}}, "compileOnly": true}, "System.ServiceModel.Web/*******": {"compile": {"System.ServiceModel.Web.dll": {}}, "compileOnly": true}, "System.ServiceProcess/*******": {"compile": {"System.ServiceProcess.dll": {}}, "compileOnly": true}, "System.Text.Encoding.CodePages/10.0.0.0": {"compile": {"System.Text.Encoding.CodePages.dll": {}}, "compileOnly": true}, "System.Text.Encoding/10.0.0.0": {"compile": {"System.Text.Encoding.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Extensions/10.0.0.0": {"compile": {"System.Text.Encoding.Extensions.dll": {}}, "compileOnly": true}, "System.Text.Encodings.Web/10.0.0.0": {"compile": {"System.Text.Encodings.Web.dll": {}}, "compileOnly": true}, "System.Text.Json/10.0.0.0": {"compile": {"System.Text.Json.dll": {}}, "compileOnly": true}, "System.Text.RegularExpressions/10.0.0.0": {"compile": {"System.Text.RegularExpressions.dll": {}}, "compileOnly": true}, "System.Threading.AccessControl/10.0.0.0": {"compile": {"System.Threading.AccessControl.dll": {}}, "compileOnly": true}, "System.Threading.Channels/10.0.0.0": {"compile": {"System.Threading.Channels.dll": {}}, "compileOnly": true}, "System.Threading/10.0.0.0": {"compile": {"System.Threading.dll": {}}, "compileOnly": true}, "System.Threading.Overlapped/10.0.0.0": {"compile": {"System.Threading.Overlapped.dll": {}}, "compileOnly": true}, "System.Threading.RateLimiting/10.0.0.0": {"compile": {"System.Threading.RateLimiting.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Dataflow/10.0.0.0": {"compile": {"System.Threading.Tasks.Dataflow.dll": {}}, "compileOnly": true}, "System.Threading.Tasks/10.0.0.0": {"compile": {"System.Threading.Tasks.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Extensions/10.0.0.0": {"compile": {"System.Threading.Tasks.Extensions.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Parallel/10.0.0.0": {"compile": {"System.Threading.Tasks.Parallel.dll": {}}, "compileOnly": true}, "System.Threading.Thread/10.0.0.0": {"compile": {"System.Threading.Thread.dll": {}}, "compileOnly": true}, "System.Threading.ThreadPool/10.0.0.0": {"compile": {"System.Threading.ThreadPool.dll": {}}, "compileOnly": true}, "System.Threading.Timer/10.0.0.0": {"compile": {"System.Threading.Timer.dll": {}}, "compileOnly": true}, "System.Transactions/*******": {"compile": {"System.Transactions.dll": {}}, "compileOnly": true}, "System.Transactions.Local/10.0.0.0": {"compile": {"System.Transactions.Local.dll": {}}, "compileOnly": true}, "System.ValueTuple/10.0.0.0": {"compile": {"System.ValueTuple.dll": {}}, "compileOnly": true}, "System.Web/*******": {"compile": {"System.Web.dll": {}}, "compileOnly": true}, "System.Web.HttpUtility/10.0.0.0": {"compile": {"System.Web.HttpUtility.dll": {}}, "compileOnly": true}, "System.Windows/*******": {"compile": {"System.Windows.dll": {}}, "compileOnly": true}, "System.Xml/*******": {"compile": {"System.Xml.dll": {}}, "compileOnly": true}, "System.Xml.Linq/*******": {"compile": {"System.Xml.Linq.dll": {}}, "compileOnly": true}, "System.Xml.ReaderWriter/10.0.0.0": {"compile": {"System.Xml.ReaderWriter.dll": {}}, "compileOnly": true}, "System.Xml.Serialization/*******": {"compile": {"System.Xml.Serialization.dll": {}}, "compileOnly": true}, "System.Xml.XDocument/10.0.0.0": {"compile": {"System.Xml.XDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlDocument/10.0.0.0": {"compile": {"System.Xml.XmlDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlSerializer/10.0.0.0": {"compile": {"System.Xml.XmlSerializer.dll": {}}, "compileOnly": true}, "System.Xml.XPath/10.0.0.0": {"compile": {"System.Xml.XPath.dll": {}}, "compileOnly": true}, "System.Xml.XPath.XDocument/10.0.0.0": {"compile": {"System.Xml.XPath.XDocument.dll": {}}, "compileOnly": true}, "WindowsBase/*******": {"compile": {"WindowsBase.dll": {}}, "compileOnly": true}, "coverlet.collector/6.0.2": {"compileOnly": true}, "Microsoft.Build.Framework/17.8.3": {"compile": {"ref/net8.0/Microsoft.Build.Framework.dll": {}}, "compileOnly": true}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"compileOnly": true}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.1": {"compileOnly": true}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"compileOnly": true}, "Microsoft.Extensions.Caching.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Caching.Memory/9.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Configuration/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Binder/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"compileOnly": true}, "Microsoft.Extensions.Diagnostics/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"compileOnly": true}, "Microsoft.Extensions.Hosting/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Binder": "9.0.1", "Microsoft.Extensions.Configuration.CommandLine": "9.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Diagnostics": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.Hosting.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Configuration": "9.0.0", "Microsoft.Extensions.Logging.Console": "9.0.0", "Microsoft.Extensions.Logging.Debug": "9.0.0", "Microsoft.Extensions.Logging.EventLog": "9.0.0", "Microsoft.Extensions.Logging.EventSource": "9.0.0", "Microsoft.Extensions.Options": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Http/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Diagnostics": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Logging/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Logging.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Binder": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Configuration": "9.0.0", "Microsoft.Extensions.Options": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventLog/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "System.Diagnostics.EventLog": "9.0.0"}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Options/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.Binder": "9.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "compileOnly": true}, "Microsoft.Extensions.Primitives/9.0.1": {"compileOnly": true}, "Microsoft.NETCore.Platforms/1.1.0": {"compileOnly": true}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}, "compileOnly": true}, "xunit.analyzers/1.16.0": {"compileOnly": true}, "xunit.runner.visualstudio/2.8.2": {"compileOnly": true}}}, "libraries": {"marketdata-service.Tests/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Castle.Core/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "path": "castle.core/5.1.1", "hashPath": "castle.core.5.1.1.nupkg.sha512"}, "Docker.DotNet/3.125.15": {"type": "package", "serviceable": true, "sha512": "sha512-XN8FKxVv8Mjmwu104/Hl9lM61pLY675s70gzwSj8KR5pwblo8HfWLcCuinh9kYsqujBkMH4HVRCEcRuU6al4BQ==", "path": "docker.dotnet/3.125.15", "hashPath": "docker.dotnet.3.125.15.nupkg.sha512"}, "Docker.DotNet.X509/3.125.15": {"type": "package", "serviceable": true, "sha512": "sha512-ONQN7ImrL3tHStUUCCPHwrFFQVpIpE+7L6jaDAMwSF+yTEmeWBmRARQZDRuvfj/+WtB8RR0oTW0tT3qQMSyHOw==", "path": "docker.dotnet.x509/3.125.15", "hashPath": "docker.dotnet.x509.3.125.15.nupkg.sha512"}, "DotNetEnv/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-o4SqUVCq0pqHF/HYsZk6k22XGIVmvsDVo+Dy7l0ubq9uQ45JkXswrMRJmYvhGLXWFYF0M5OupMonytB+0zvpGQ==", "path": "dotnetenv/3.1.1", "hashPath": "dotnetenv.3.1.1.nupkg.sha512"}, "FluentAssertions/6.12.2": {"type": "package", "serviceable": true, "sha512": "sha512-8YE+xJmT8wgzEpFuzJ4S62oFhEL/AKouMz1RWPEMEUhy9H11aRQlGIWcHurH5BEy7tbF6gb0CJrs0wOw/AtDcQ==", "path": "fluentassertions/6.12.2", "hashPath": "fluentassertions.6.12.2.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bs+1Pq3vQdS2lTyxNUd9fEhtMsq3eLUpK36k2t56iDMVrk6OrAoFtvrQrTK0Y0OetTcJrUkGU7hBlf+ORzHLqQ==", "path": "microsoft.aspnetcore.authentication.jwtbearer/9.0.0", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Testing/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4tyGN2cb2lVqMwqPhDhXAkTtSci8RJ0cFKVHEU3yj6I4krcbyQE6SJmAQr5Hq8ARyVopKUrQp/qniDje/1I07A==", "path": "microsoft.aspnetcore.mvc.testing/9.0.0", "hashPath": "microsoft.aspnetcore.mvc.testing.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FqUK5j1EOPNuFT7IafltZQ3cakqhSwVzH5ZW1MhZDe4pPXs9sJ2M5jom1Omsu+mwF2tNKKlRAzLRHQTZzbd+6Q==", "path": "microsoft.aspnetcore.openapi/9.0.0", "hashPath": "microsoft.aspnetcore.openapi.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.TestHost/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T5t8Qac05kJtFzsBxo+B3p0UcLNTRoWQf/1EbpaVBw9d7w2xL6RKYh0mqG+rPn2rulJDKeU3VfAd+r/YHdaKBg==", "path": "microsoft.aspnetcore.testhost/9.0.0", "hashPath": "microsoft.aspnetcore.testhost.9.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.Build.Locator/1.7.8": {"type": "package", "serviceable": true, "sha512": "sha512-sPy10x527Ph16S2u0yGME4S6ohBKJ69WfjeGG/bvELYeZVmJdKjxgnlL8cJJJLGV/cZIRqSfB12UDB8ICakOog==", "path": "microsoft.build.locator/1.7.8", "hashPath": "microsoft.build.locator.1.7.8.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-IEYreI82QZKklp54yPHxZNG9EKSK6nHEkeuf+0Asie9llgS1gp0V1hw7ODG+QyoB7MuAnNQHmeV1Per/ECpv6A==", "path": "microsoft.codeanalysis.workspaces.msbuild/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512"}, "Microsoft.CodeCoverage/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-4svMznBd5JM21JIG2xZKGNanAHNXplxf/kQDFfLHXQ3OnpJkayRK/TjacFjA+EYmoyuNXHo/sOETEfcYtAzIrA==", "path": "microsoft.codecoverage/17.12.0", "hashPath": "microsoft.codecoverage.17.12.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-E25w4XugXNykTr5Y/sLDGaQ4lf67n9aXVPvsdGsIZjtuLmbvb9AoYP8D50CDejY8Ro4D9GK2kNHz5lWHqSK+wg==", "path": "microsoft.entityframeworkcore/9.0.1", "hashPath": "microsoft.entityframeworkcore.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qy+taGVLUs82zeWfc32hgGL8Z02ZqAneYvqZiiXbxF4g4PBUcPRuxHM9K20USmpeJbn4/fz40GkCbyyCy5ojOA==", "path": "microsoft.entityframeworkcore.abstractions/9.0.1", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/pchcadGU57ChRYH0/bvLTeU/n1mpWO+0pVK7pUzzuwRu5SIQb8dVMZVPhzvEI2VO5rP1yricSQBBnOmDqQhvg==", "path": "microsoft.entityframeworkcore.design/9.0.1", "hashPath": "microsoft.entityframeworkcore.design.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.InMemory/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XTuTax42m89JET3rgjWphx11pGXD77dc9kAO8B4l9Ze9NAJnw7qDL/cqmaGiHEAxL/8f0PJ+zY4FII0v/nXlmg==", "path": "microsoft.entityframeworkcore.inmemory/9.0.1", "hashPath": "microsoft.entityframeworkcore.inmemory.9.0.1.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-7Iu0h4oevRvH4IwPzmxuIJGYRt55TapoREGlluk75KCO7lenN0+QnzCl6cQDY48uDoxAUpJbpK2xW7o8Ix69dw==", "path": "microsoft.entityframeworkcore.relational/9.0.1", "hashPath": "microsoft.entityframeworkcore.relational.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FHPy9cbb0y09riEpsrU5XYpOgf4nTfHj7a0m1wLC5DosGtjJn9g03gGg1GTJmEdRFBQrJwbwTnHqLCdNLsoYgA==", "path": "microsoft.extensions.dependencymodel/9.0.1", "hashPath": "microsoft.extensions.dependencymodel.9.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OtlIWcyX01olfdevPKZdIPfBEvbcioDyBiE/Z2lHsopsMD7twcKtlN9kMevHmI5IIPhFpfwCIiR6qHQz1WHUIw==", "path": "microsoft.identitymodel.abstractions/8.0.1", "hashPath": "microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s6++gF9x0rQApQzOBbSyp4jUaAlwm+DroKfL8gdOHxs83k8SJfUXhuc46rDB3rNXBQ1MVRxqKUrqFhO/M0E97g==", "path": "microsoft.identitymodel.jsonwebtokens/8.0.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UCPF2exZqBXe7v/6sGNiM6zCQOUXXQ9+v5VTb9gPB8ZSUPnX53BxlN78v2jsbIvK9Dq4GovQxo23x8JgWvm/Qg==", "path": "microsoft.identitymodel.logging/8.0.1", "hashPath": "microsoft.identitymodel.logging.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "path": "microsoft.identitymodel.protocols/8.0.1", "hashPath": "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kDimB6Dkd3nkW2oZPDkMkVHfQt3IDqO5gL0oa8WVy3OP4uE8Ij+8TXnqg9TOd9ufjsY3IDiGz7pCUbnfL18tjg==", "path": "microsoft.identitymodel.tokens/8.0.1", "hashPath": "microsoft.identitymodel.tokens.8.0.1.nupkg.sha512"}, "Microsoft.NET.Test.Sdk/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-kt/PKBZ91rFCWxVIJZSgVLk+YR+4KxTuHf799ho8WNiK5ZQpJNAEZCAWX86vcKrs+DiYjiibpYKdGZP6+/N17w==", "path": "microsoft.net.test.sdk/17.12.0", "hashPath": "microsoft.net.test.sdk.17.12.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.22": {"type": "package", "serviceable": true, "sha512": "sha512-aBvunmrdu/x+4CaA/UP1Jx4xWGwk4kymhoIRnn2Vp+zi5/KOPQJ9EkSXHRUr01WcGKtYl3Au7XfkPJbU1G2sjQ==", "path": "microsoft.openapi/1.6.22", "hashPath": "microsoft.openapi.1.6.22.nupkg.sha512"}, "Microsoft.TestPlatform.ObjectModel/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-TDqkTKLfQuAaPcEb3pDDWnh7b3SyZF+/W9OZvWFp6eJCIiiYFdSB6taE2I6tWrFw5ywhzOb6sreoGJTI6m3rSQ==", "path": "microsoft.testplatform.objectmodel/17.12.0", "hashPath": "microsoft.testplatform.objectmodel.17.12.0.nupkg.sha512"}, "Microsoft.TestPlatform.TestHost/17.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-MiPEJQNyADfwZ4pJNpQex+t9/jOClBGMiCiVVFuELCMSX2nmNfvUor3uFVxNNCg30uxDP8JDYfPnMXQzsfzYyg==", "path": "microsoft.testplatform.testhost/17.12.0", "hashPath": "microsoft.testplatform.testhost.17.12.0.nupkg.sha512"}, "Mono.TextTemplating/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "path": "mono.texttemplating/3.0.0", "hashPath": "mono.texttemplating.3.0.0.nupkg.sha512"}, "Moq/4.20.72": {"type": "package", "serviceable": true, "sha512": "sha512-EA55cjyNn8eTNWrgrdZJH5QLFp2L43oxl1tlkoYUKIE9pRwL784OWiTXeCV5ApS+AMYEAlt7Fo03A2XfouvHmQ==", "path": "moq/4.20.72", "hashPath": "moq.4.20.72.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Npgsql/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tPvY61CxOAWxNsKLEBg+oR646X4Bc8UmyQ/tJszL/7mEmIXQnnBhVJZrZEEUv0Bstu0mEsHZD5At3EO8zQRAYw==", "path": "npgsql/9.0.3", "hashPath": "npgsql.9.0.3.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-mw5vcY2IEc7L+IeGrxpp/J5OSnCcjkjAgJYCm/eD52wpZze8zsSifdqV7zXslSMmfJG2iIUGZyo3KuDtEFKwMQ==", "path": "npgsql.entityframeworkcore.postgresql/9.0.4", "hashPath": "npgsql.entityframeworkcore.postgresql.9.0.4.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-zG2FApP5zxSx6OcdJQLbZDk2AVlN2BNQD6MorwIfV6gVj0RRxWPEp2LXAxqDGZqeNV1Zp0BNPcNaey/GXmTdvQ==", "path": "pipelines.sockets.unofficial/2.2.8", "hashPath": "pipelines.sockets.unofficial.2.2.8.nupkg.sha512"}, "SharpZipLib/1.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "path": "sharpziplib/1.4.2", "hashPath": "sharpziplib.1.4.2.nupkg.sha512"}, "Sprache/2.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-Q+mXeiTxiUYG3lKYF6TS82/SyB4F2613Q1yXTMwg4jWGHEEVC3yrzHtNcI4B3qnDI0+eJsezGJ0V+cToUytHWw==", "path": "sprache/2.3.1", "hashPath": "sprache.2.3.1.nupkg.sha512"}, "SSH.NET/2023.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g+3VDUrYhm0sqSxmlQFgRFrmBxhQvVh4pfn4pqjkX7WXE3tTjt1tIsOtjuz3mz/5s8gFFQVRydwCJ7Ohs54sJA==", "path": "ssh.net/2023.0.0", "hashPath": "ssh.net.2023.0.0.nupkg.sha512"}, "SshNet.Security.Cryptography/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5pBIXRjcSO/amY8WztpmNOhaaCNHY/B6CcYDI7FSTgqSyo/ZUojlLiKcsl+YGbxQuLX439qIkMfP0PHqxqJi/Q==", "path": "sshnet.security.cryptography/1.3.0", "hashPath": "sshnet.security.cryptography.1.3.0.nupkg.sha512"}, "StackExchange.Redis/2.8.16": {"type": "package", "serviceable": true, "sha512": "sha512-WaoulkOqOC9jHepca3JZKFTqndCWab5uYS7qCzmiQDlrTkFaDN7eLSlEfHycBxipRnQY9ppZM7QSsWAwUEGblw==", "path": "stackexchange.redis/2.8.16", "hashPath": "stackexchange.redis.2.8.16.nupkg.sha512"}, "StackExchange.Redis.Extensions.Core/10.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-wpgpVdZIm7sw5rx19SGXbPWeVBjDFVjxDa3GWUMZRcrbmOMCNNA54+7xPQP70pUZmDYuEYU1fHyb/HU0X1+Mqg==", "path": "stackexchange.redis.extensions.core/10.2.0", "hashPath": "stackexchange.redis.extensions.core.10.2.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/7.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-vJv19UpWm6OOgnS9QLDnWARNVasXUfj8SFvlG7UVALm4nBnfwRnEky7C0veSDqMUmBeMPC6Ec3d6G1ts/J04Uw==", "path": "swashbuckle.aspnetcore/7.2.0", "hashPath": "swashbuckle.aspnetcore.7.2.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/7.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-y27fNDfIh1vGhJjXYynLcZjl7DLOW1bSO2MDsY9wB4Zm1fdxpPsuBSiR4U+0acWlAqLmnuOPKr/OeOgwRUkBlw==", "path": "swashbuckle.aspnetcore.swagger/7.2.0", "hashPath": "swashbuckle.aspnetcore.swagger.7.2.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/7.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-pMrTxGVuXM7t4wqft5CNNU8A0++Yw5kTLmYhB6tbEcyBfO8xEF/Y8pkJhO6BZ/2MYONrRYoQTfPFJqu8fOf5WQ==", "path": "swashbuckle.aspnetcore.swaggergen/7.2.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.7.2.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/7.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hgrXeKzyp5OGN8qVvL7A+vhmU7mDJTfGpiMBRL66IcfLOyna8UTLtn3cC3CghamXpRDufcc9ciklTszUGEQK0w==", "path": "swashbuckle.aspnetcore.swaggerui/7.2.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.7.2.0.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "path": "system.composition/7.0.0", "hashPath": "system.composition.7.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "path": "system.composition.attributedmodel/7.0.0", "hashPath": "system.composition.attributedmodel.7.0.0.nupkg.sha512"}, "System.Composition.Convention/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "path": "system.composition.convention/7.0.0", "hashPath": "system.composition.convention.7.0.0.nupkg.sha512"}, "System.Composition.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "path": "system.composition.hosting/7.0.0", "hashPath": "system.composition.hosting.7.0.0.nupkg.sha512"}, "System.Composition.Runtime/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "path": "system.composition.runtime/7.0.0", "hashPath": "system.composition.runtime.7.0.0.nupkg.sha512"}, "System.Composition.TypedParts/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "path": "system.composition.typedparts/7.0.0", "hashPath": "system.composition.typedparts.7.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-gWwQv/Ug1qWJmHCmN17nAbxJYmQBM/E94QxKLksvUiiKB1Ld3Sc/eK1lgmbSjDFxkQhVuayI/cGFZhpBSodLrg==", "path": "system.configuration.configurationmanager/4.4.0", "hashPath": "system.configuration.configurationmanager.4.4.0.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qd01+AqPhbAG14KtdtIqFk+cxHQFZ/oqRSCoxU1F+Q6Kv0cl726sl7RzU9yLFGd4BUOKdN4XojXF0pQf/R6YeA==", "path": "system.diagnostics.eventlog/9.0.0", "hashPath": "system.diagnostics.eventlog.9.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "path": "system.identitymodel.tokens.jwt/8.0.1", "hashPath": "system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-cJV7ScGW7EhatRsjehfvvYVBvtiSMKgN8bOVI0bQhnF5bU7vnHVIsH49Kva7i7GWaWYvmEzkYVk1TC+gZYBEog==", "path": "system.security.cryptography.protecteddata/4.4.0", "hashPath": "system.security.cryptography.protecteddata.4.4.0.nupkg.sha512"}, "Testcontainers/3.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-jrWWOQYpBpPhAtVUgXiI0KLmgKK6/rwNYrCz1hC9MKbERTwVKukOTAagtOcma16xgSCsV0dvyo9pB25pOSx4zQ==", "path": "testcontainers/3.9.0", "hashPath": "testcontainers.3.9.0.nupkg.sha512"}, "Testcontainers.Redis/3.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-7AfCeIYkSA3n7EmbUIPojQD7IQsyJGgyo/fSAmdeHOzr2909AKfg1VqnSLqxO6xfSZcpkKXGe2LC4TK06w6KLg==", "path": "testcontainers.redis/3.9.0", "hashPath": "testcontainers.redis.3.9.0.nupkg.sha512"}, "xunit/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-7LhFS2N9Z6Xgg8aE5lY95cneYivRMfRI8v+4PATa4S64D5Z/Plkg0qa8dTRHSiGRgVZ/CL2gEfJDE5AUhOX+2Q==", "path": "xunit/2.9.2", "hashPath": "xunit.2.9.2.nupkg.sha512"}, "xunit.abstractions/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-pot1I4YOxlWjIb5jmwvvQNbTrZ3lJQ+jUGkGjWE3hEFM0l5gOnBWS+H3qsex68s5cO52g+44vpGzhAt+42vwKg==", "path": "xunit.abstractions/2.0.3", "hashPath": "xunit.abstractions.2.0.3.nupkg.sha512"}, "xunit.assert/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-QkNBAQG4pa66cholm28AxijBjrmki98/vsEh4Sx5iplzotvPgpiotcxqJQMRC8d7RV7nIT8ozh97957hDnZwsQ==", "path": "xunit.assert/2.9.2", "hashPath": "xunit.assert.2.9.2.nupkg.sha512"}, "xunit.core/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-O6RrNSdmZ0xgEn5kT927PNwog5vxTtKrWMihhhrT0Sg9jQ7iBDciYOwzBgP2krBEk5/GBXI18R1lKvmnxGcb4w==", "path": "xunit.core/2.9.2", "hashPath": "xunit.core.2.9.2.nupkg.sha512"}, "xunit.extensibility.core/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-Ol+KlBJz1x8BrdnhN2DeOuLrr1I/cTwtHCggL9BvYqFuVd/TUSzxNT5O0NxCIXth30bsKxgMfdqLTcORtM52yQ==", "path": "xunit.extensibility.core/2.9.2", "hashPath": "xunit.extensibility.core.2.9.2.nupkg.sha512"}, "xunit.extensibility.execution/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-rKMpq4GsIUIJibXuZoZ8lYp5EpROlnYaRpwu9Zr0sRZXE7JqJfEEbCsUriZqB+ByXCLFBJyjkTRULMdC+U566g==", "path": "xunit.extensibility.execution/2.9.2", "hashPath": "xunit.extensibility.execution.2.9.2.nupkg.sha512"}, "MarketDataService/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Antiforgery/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.BearerToken/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Cookies/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Core/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.OAuth/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Policy/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Authorization/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Endpoints/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Forms/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Server/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Web/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.CookiePolicy/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cors/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.Internal/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HostFiltering/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Html.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections.Common/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Features/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Results/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpLogging/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpOverrides/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpsPolicy/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Identity/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization.Routing/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Metadata/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ApiExplorer/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Core/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Cors/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.DataAnnotations/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Json/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Localization/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Razor/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.RazorPages/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.TagHelpers/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ViewFeatures/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.OutputCaching/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RateLimiting/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Runtime/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.RequestDecompression/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCompression/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Rewrite/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.HttpSys/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IIS/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IISIntegration/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Core/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Session/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Common/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Core/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Protocols.Json/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticAssets/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticFiles/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebSockets/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebUtilities/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.CSharp/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Abstractions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Caching.Memory.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Abstractions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Binder.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.CommandLine.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Ini/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Json.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.KeyPerFile/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Xml/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Abstractions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.Abstractions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Features/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Composite/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Embedded/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Physical.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileSystemGlobbing.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Abstractions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Http.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Core/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Stores/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization.Abstractions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Abstractions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Configuration.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Console.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Debug.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventLog.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventSource.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.TraceSource/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.ObjectPool/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.DataAnnotations/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Primitives.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Validation/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.WebEncoders/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.JSInterop/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Net.Http.Headers/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic.Core/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Registry/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "mscorlib/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/2.1.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Buffers/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Immutable/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Annotations/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.DataSetExtensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.DiagnosticSource/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.EventLog.Reference/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tracing/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Asn1/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Cbor/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Formats.Tar/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Brotli/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.AccessControl/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipelines/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes.AccessControl/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.AsyncEnumerable/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Memory/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http.Json/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.HttpListener/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Mail/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Quic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServerSentEvents/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServicePoint/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebClient/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebProxy/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics.Vectors/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.DispatchProxy/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Metadata/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.TypeExtensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.Unsafe/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.JavaScript/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Intrinsics/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Loader/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.AccessControl/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Cng/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.OpenSsl/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Xml/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Windows/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceProcess/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.CodePages/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encodings.Web/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Json/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.AccessControl/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Channels/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.RateLimiting/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Dataflow/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Extensions/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions.Local/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ValueTuple/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web.HttpUtility/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "WindowsBase/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "coverlet.collector/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-bJShQ6uWRTQ100ZeyiMqcFlhP7WJ+bCuabUs885dJiBEzMsJMSFr7BOyeCw4rgvQokteGi5rKQTlkhfQPUXg2A==", "path": "coverlet.collector/6.0.2", "hashPath": "coverlet.collector.6.0.2.nupkg.sha512"}, "Microsoft.Build.Framework/17.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-NrQZJW8TlKVPx72yltGb8SVz3P5mNRk9fNiD/ao8jRSk48WqIIdCn99q4IjlVmPcruuQ+yLdjNQLL8Rb4c916g==", "path": "microsoft.build.framework/17.8.3", "hashPath": "microsoft.build.framework.17.8.3.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-c6ZZJZhPKrXFkE2z/81PmuT69HBL6Y68Cl0xJ5SRrDjJyq5Aabkq15yCqPg9RQ3R0aFLVaJok2DA8R3TKpejDQ==", "path": "microsoft.entityframeworkcore.analyzers/9.0.1", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Eghsg9SyIvq0c8x6cUpe71BbQoOmsytXxqw2+ZNiTnP8a8SBLKgEor1zZeWhC0588IbS2M0PP4gXGAd9qF862Q==", "path": "microsoft.extensions.caching.abstractions/9.0.1", "hashPath": "microsoft.extensions.caching.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Je<PERSON>+PP0BCKMwwLezPGDaciJSTfcFG4KjsG8rX4XZ6RSvzdxofrFmcnmW2L4+cWUcZSBTQ+Dd7H5Gs9XZz/OlCA==", "path": "microsoft.extensions.caching.memory/9.0.1", "hashPath": "microsoft.extensions.caching.memory.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-VuthqFS+ju6vT8W4wevdhEFiRi1trvQtkzWLonApfF5USVzzDcTBoY3F24WvN/tffLSrycArVfX1bThm/9xY2A==", "path": "microsoft.extensions.configuration/9.0.1", "hashPath": "microsoft.extensions.configuration.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+4hfFIY1UjBCXFTTOd+ojlDPq6mep3h5Vq5SYE3Pjucr7dNXmq4S/6P/LoVnZFz2e/5gWp/om4svUFgznfULcA==", "path": "microsoft.extensions.configuration.abstractions/9.0.1", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-w7kAyu1Mm7eParRV6WvGNNwA8flPTub16fwH49h7b/yqJZFTgYxnOVCuiah3G2bgseJMEq4DLjjsyQRvsdzRgA==", "path": "microsoft.extensions.configuration.binder/9.0.1", "hashPath": "microsoft.extensions.configuration.binder.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qD+hdkBtR9Ps7AxfhTJCnoVakkadHgHlD1WRN0QHGHod+SDuca1ao1kF4G2rmpAz2AEKrE2N2vE8CCCZ+ILnNw==", "path": "microsoft.extensions.configuration.commandline/9.0.0", "hashPath": "microsoft.extensions.configuration.commandline.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-v5R638eNMxksfXb7MFnkPwLPp+Ym4W/SIGNuoe8qFVVyvygQD5DdLusybmYSJEr9zc1UzWzim/ATKeIOVvOFDg==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4EK93Jcd2lQG4GY6PAw8jGss0ZzFP0vPc1J85mES5fKNuDTqgFXHba9onBw2s18fs3I4vdo2AWyfD1mPAxWSQQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WiTK0LrnsqmedrbzwL7f4ZUo+/wByqy2eKab39I380i2rd8ImfCRMrtkqJVGDmfqlkP/YzhckVOwPc5MPrSNpg==", "path": "microsoft.extensions.configuration.json/9.0.0", "hashPath": "microsoft.extensions.configuration.json.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FShWw8OysquwV7wQHYkkz0VWsJSo6ETUu4h7tJRMtnG0uR+tzKOldhcO8xB1pGSOI3Ng6v3N1Q94YO8Rzq1P6A==", "path": "microsoft.extensions.configuration.usersecrets/9.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qZI42ASAe3hr2zMSA6UjM92pO1LeDq5DcwkgSowXXPY8I56M76pEKrnmsKKbxagAf39AJxkH2DY4sb72ixyOrg==", "path": "microsoft.extensions.dependencyinjection/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Tr74eP0oQ3AyC24ch17N8PuEkrPbD0JqIfENCYqmgKYNOmL8wQKzLJu3ObxTUDrjnn4rHoR1qKa37/eQyHmCDA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4ZmP6turxMFsNwK/MCko2fuIITaYYN/eXyyIRq1FjLDKnptdbn6xMb7u0zfSMzCGpzkx4RxH/g1jKN2IchG7uA==", "path": "microsoft.extensions.diagnostics/9.0.1", "hashPath": "microsoft.extensions.diagnostics.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-pfAPuVtHvG6dvZtAa0OQbXdDqq6epnr8z0/IIUjdmV0tMeI8Aj9KxDXvdDvqr+qNHTkmA7pZpChNxwNZt4GXVg==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.1", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uK439QzYR0q2emLVtYzwyK3x+T5bTY4yWsd/k/ZUS9LR6Sflp8MIdhGXW8kQCd86dQD4tLqvcbLkku8qHY263Q==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3+ZUSpOSmie+o8NnLIRqCxSh65XL/ExU7JYnFOg58awDRlY3lVpZ9A369jkoZL1rpsq7LDhEfkn2ghhGaY1y5Q==", "path": "microsoft.extensions.fileproviders.physical/9.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jGFKZiXs2HNseK3NK/rfwHNNovER71jSj4BD1a/649ml9+h6oEtYd0GSALZDNW8jZ2Rh+oAeadOa6sagYW1F2A==", "path": "microsoft.extensions.filesystemglobbing/9.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wNmQWRCa83HYbpxQ3wH7xBn8oyGjONSj1k8svzrFUFyJMfg/Ja/g0NfI0p85wxlUxBh97A6ypmL8X5vVUA5y2Q==", "path": "microsoft.extensions.hosting/9.0.0", "hashPath": "microsoft.extensions.hosting.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yUKJgu81ExjvqbNWqZKshBbLntZMbMVz/P7Way2SBx7bMqA08Mfdc9O7hWDKAiSp+zPUGT6LKcSCQIPeDK+CCw==", "path": "microsoft.extensions.hosting.abstractions/9.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-j1UmqmTRIc0OJhv8feVFmXhPS/Z+82o/JLF3WKlydC3esolPVVJPJ0oq/MSECXFZMBKVVpxUBJnR6dJH1hTWzQ==", "path": "microsoft.extensions.http/9.0.1", "hashPath": "microsoft.extensions.http.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-E/k5r7S44DOW+08xQPnNbO8DKAQHhkspDboTThNJ6Z3/QBb4LC6gStNWzVmy3IvW7sUD+iJKf4fj0xEkqE7vnQ==", "path": "microsoft.extensions.logging/9.0.1", "hashPath": "microsoft.extensions.logging.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-w2gUqXN/jNIuvqYwX3lbXagsizVNXYyt6LlF57+tMve4JYCEgCMMAjRce6uKcDASJgpMbErRT1PfHy2OhbkqEA==", "path": "microsoft.extensions.logging.abstractions/9.0.1", "hashPath": "microsoft.extensions.logging.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-H05HiqaNmg6GjH34ocYE9Wm1twm3Oz2aXZko8GTwGBzM7op2brpAA8pJ5yyD1OpS1mXUtModBYOlcZ/wXeWsSg==", "path": "microsoft.extensions.logging.configuration/9.0.0", "hashPath": "microsoft.extensions.logging.configuration.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yDZ4zsjl7N0K+R/1QTNpXBd79Kaf4qNLHtjk4NaG82UtNg2Z6etJywwv6OarOv3Rp7ocU7uIaRY4CrzHRO/d3w==", "path": "microsoft.extensions.logging.console/9.0.0", "hashPath": "microsoft.extensions.logging.console.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4wGlHsrLhYjLw4sFkfRixu2w4DK7dv60OjbvgbLGhUJk0eUPxYHhnszZ/P18nnAkfrPryvtOJ3ZTVev0kpqM6A==", "path": "microsoft.extensions.logging.debug/9.0.0", "hashPath": "microsoft.extensions.logging.debug.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/B8I5bScondnLMNULA3PBu/7Gvmv/P7L83j7gVrmLh6R+HCgHqUNIwVvzCok4ZjIXN2KxrsONHjFYwoBK5EJgQ==", "path": "microsoft.extensions.logging.eventlog/9.0.0", "hashPath": "microsoft.extensions.logging.eventlog.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zvSjdOAb3HW3aJPM5jf+PR9UoIkoci9id80RXmBgrDEozWI0GDw8tdmpyZgZSwFDvGCwHFodFLNQaeH8879rlA==", "path": "microsoft.extensions.logging.eventsource/9.0.0", "hashPath": "microsoft.extensions.logging.eventsource.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nggoNKnWcsBIAaOWHA+53XZWrslC7aGeok+aR+epDPRy7HI7GwMnGZE8yEsL2Onw7kMOHVHwKcsDls1INkNUJQ==", "path": "microsoft.extensions.options/9.0.1", "hashPath": "microsoft.extensions.options.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-8RRKWtuU4fR+8MQLR/8CqZwZ9yc2xCpllw/WPRY7kskIqEq0hMcEI4AfUJO72yGiK2QJkrsDcUvgB5Yc+3+lyg==", "path": "microsoft.extensions.options.configurationextensions/9.0.1", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-bHtTesA4lrSGD1ZUaMIx6frU3wyy0vYtTa/hM6gGQu5QNrydObv8T5COiGUWsisflAfmsaFOe9Xvw5NSO99z0g==", "path": "microsoft.extensions.primitives/9.0.1", "hashPath": "microsoft.extensions.primitives.9.0.1.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "xunit.analyzers/1.16.0": {"type": "package", "serviceable": true, "sha512": "sha512-hptYM7vGr46GUIgZt21YHO4rfuBAQS2eINbFo16CV/Dqq+24Tp+P5gDCACu1AbFfW4Sp/WRfDPSK8fmUUb8s0Q==", "path": "xunit.analyzers/1.16.0", "hashPath": "xunit.analyzers.1.16.0.nupkg.sha512"}, "xunit.runner.visualstudio/2.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-vm1tbfXhFmjFMUmS4M0J0ASXz3/U5XvXBa6DOQUL3fEz4Vt6YPhv+ESCarx6M6D+9kJkJYZKCNvJMas1+nVfmQ==", "path": "xunit.runner.visualstudio/2.8.2", "hashPath": "xunit.runner.visualstudio.2.8.2.nupkg.sha512"}}}